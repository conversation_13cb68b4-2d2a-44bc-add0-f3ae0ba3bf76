#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多普勒频移效应演示 - 单对信号版本
显示Chirp信号受CFO+多普勒效应影响的频率偏移
只显示一对信号：理论值（实线）和接收值（虚线）
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from scipy import ndimage

# 设置学术风格
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.linewidth'] = 1.2

def create_single_pair_doppler_fft():
    """创建单对信号的多普勒频移FFT图"""

    # 创建频率轴
    freqs = np.linspace(2, 4, 2000)

    # 初始化噪声背景（使用灰色）
    np.random.seed(42)
    noise_background = np.random.normal(0, 0.02, len(freqs))
    noise_background = np.abs(noise_background)

    # 定义理论值和实际值的频率（CFO+多普勒偏移）
    freq_offset = 0.035  # 35Hz的CFO+多普勒偏移

    # 选择中心频率位置
    freq_theory = 3.0   # 理论频率值
    freq_actual = freq_theory + freq_offset  # 实际接收频率值

    # 创建多层次的自然噪声背景
    n_points = len(freqs)

    # 1. 基础随机噪声
    base_noise = 0.04 + 0.03 * np.random.randn(n_points)

    # 2. 添加平滑的低频变化
    x = np.arange(n_points)
    low_freq_variation = 0.02 * np.sin(2 * np.pi * 0.05 * x / n_points)

    # 3. 添加中频变化
    mid_freq_variation = 0.015 * np.sin(2 * np.pi * 0.2 * x / n_points + np.pi/3)

    # 4. 添加高频细节
    high_freq_variation = 0.01 * np.sin(2 * np.pi * 0.8 * x / n_points + np.pi/6)

    # 5. 添加随机的局部变化
    local_variations = np.zeros(n_points)
    for _ in range(8):  # 8个局部变化区域
        center = np.random.randint(0, n_points)
        width = np.random.randint(30, 120)  # 变化区域宽度
        amplitude_var = np.random.uniform(0.008, 0.025)

        for i in range(max(0, center - width//2), min(n_points, center + width//2)):
            distance = abs(i - center)
            if distance < width//2:
                # 使用高斯函数创建平滑变化
                local_variations[i] += amplitude_var * np.exp(-2 * (distance / (width/2))**2)

    # 6. 组合所有噪声源
    background_spectrum = base_noise + low_freq_variation + mid_freq_variation + high_freq_variation + local_variations

    # 7. 确保非负且在合理范围内
    background_spectrum = np.maximum(background_spectrum, 0.01)
    background_spectrum = np.minimum(background_spectrum, 0.08)

    # 8. 应用平滑滤波使线条更圆滑
    background_spectrum = ndimage.gaussian_filter(background_spectrum, sigma=1.2)

    # 创建峰值函数
    def add_peak_to_spectrum(spectrum, center_freq, amplitude, width=15):
        result_spectrum = np.copy(spectrum)
        peak_idx = np.argmin(np.abs(freqs - center_freq))
        for i in range(-width//2, width//2 + 1):
            idx = peak_idx + i
            if 0 <= idx < len(freqs):
                peak_amp = amplitude * np.exp(-0.5 * (i / (width/6))**2)
                result_spectrum[idx] = max(result_spectrum[idx], peak_amp)
        return result_spectrum

    # 生成两个峰值频谱
    theory_amplitude = 0.75  # 理论值高度
    actual_amplitude = 0.60  # 接收值高度（比理论值明显矮一些）
    
    theory_spectrum = add_peak_to_spectrum(background_spectrum, freq_theory, theory_amplitude)
    actual_spectrum = add_peak_to_spectrum(background_spectrum, freq_actual, actual_amplitude)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 使用学术风格的配色方案
    color_signal = '#5B7C99'  # 暗钢蓝色

    # 绘制噪声背景填充（灰色）
    ax.fill_between(freqs, 0, background_spectrum, color='#2E4057', alpha=0.8)

    # 计算圆圈标记的半径
    circle_radius = 0.010

    # 绘制理论值峰值线（实线）
    noise_level_theory = background_spectrum[np.argmin(np.abs(freqs - freq_theory))]
    ax.plot([freq_theory, freq_theory], [noise_level_theory, theory_amplitude - circle_radius],
            color=color_signal, linewidth=3, alpha=0.8, linestyle='-')

    # 绘制接收值峰值线（虚线）
    noise_level_actual = background_spectrum[np.argmin(np.abs(freqs - freq_actual))]
    ax.plot([freq_actual, freq_actual], [noise_level_actual, actual_amplitude - circle_radius],
            color=color_signal, linewidth=3, alpha=0.8, linestyle='--')

    # 在峰值位置添加标记
    # 理论值（实心圆标记）
    ax.plot(freq_theory, theory_amplitude, 'o', color=color_signal, markersize=8, alpha=0.9, label='Theory')

    # 接收值（空心圆标记）
    ax.plot(freq_actual, actual_amplitude, 'o', color=color_signal, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3, label='Received')

    # 设置轴标签和刻度
    ax.set_xlabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=12, fontweight='bold')
    ax.set_xlim(2, 4)
    ax.set_ylim(0, 1)
    ax.set_xticks([2, 2.5, 3, 3.5, 4])

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 添加简单图例
    ax.legend(loc='upper right')

    # 添加CFO+多普勒频移标注
    doppler_color = '#D32F2F'  # 使用红色，更加醒目

    # 在峰值高度的60%处添加双向箭头显示频移距离
    arrow_height = theory_amplitude * 0.6
    ax.annotate('', xy=(freq_actual, arrow_height),
                xytext=(freq_theory, arrow_height),
                arrowprops=dict(arrowstyle='<->', color=doppler_color, lw=1.5,
                               shrinkA=0, shrinkB=0, mutation_scale=5))

    # CFO+多普勒频移值标注
    mid_freq = (freq_theory + freq_actual) / 2
    ax.text(mid_freq, arrow_height + 0.08, 'CFO + Doppler Shift',
            ha='center', va='center', fontsize=11, color=doppler_color, fontweight='bold')

    # 保存图像
    plt.tight_layout()
    plt.savefig('doppler_single_pair.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

def create_compensation_fft():
    """创建补偿后的FFT图 - 峰值调整到与实线相同水平"""

    # 创建频率轴
    freqs = np.linspace(2, 4, 2000)

    # 使用相同的随机种子确保背景一致
    np.random.seed(42)
    
    # 创建多层次的自然噪声背景（与主函数相同的逻辑）
    n_points = len(freqs)
    base_noise = 0.04 + 0.03 * np.random.randn(n_points)
    x = np.arange(n_points)
    low_freq_variation = 0.02 * np.sin(2 * np.pi * 0.05 * x / n_points)
    mid_freq_variation = 0.015 * np.sin(2 * np.pi * 0.2 * x / n_points + np.pi/3)
    high_freq_variation = 0.01 * np.sin(2 * np.pi * 0.8 * x / n_points + np.pi/6)
    
    local_variations = np.zeros(n_points)
    for _ in range(8):
        center = np.random.randint(0, n_points)
        width = np.random.randint(30, 120)
        amplitude_var = np.random.uniform(0.008, 0.025)
        for i in range(max(0, center - width//2), min(n_points, center + width//2)):
            distance = abs(i - center)
            if distance < width//2:
                local_variations[i] += amplitude_var * np.exp(-2 * (distance / (width/2))**2)

    background_spectrum = base_noise + low_freq_variation + mid_freq_variation + high_freq_variation + local_variations
    background_spectrum = np.maximum(background_spectrum, 0.01)
    background_spectrum = np.minimum(background_spectrum, 0.08)
    background_spectrum = ndimage.gaussian_filter(background_spectrum, sigma=1.2)

    # 创建峰值函数
    def add_peak_to_spectrum(spectrum, center_freq, amplitude, width=15):
        result_spectrum = np.copy(spectrum)
        peak_idx = np.argmin(np.abs(freqs - center_freq))
        for i in range(-width//2, width//2 + 1):
            idx = peak_idx + i
            if 0 <= idx < len(freqs):
                peak_amp = amplitude * np.exp(-0.5 * (i / (width/6))**2)
                result_spectrum[idx] = max(result_spectrum[idx], peak_amp)
        return result_spectrum

    # 补偿后的峰值 - 调整到与理论值相同的水平
    freq_compensated = 3.0  # 补偿后回到理论频率
    compensated_amplitude = 0.75  # 与理论值相同的高度
    
    compensated_spectrum = add_peak_to_spectrum(background_spectrum, freq_compensated, compensated_amplitude)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))
    color_signal = '#5B7C99'

    # 绘制噪声背景填充
    ax.fill_between(freqs, 0, background_spectrum, color='#2E4057', alpha=0.8)

    # 绘制补偿后的峰值线（虚线）
    circle_radius = 0.010
    noise_level_compensated = background_spectrum[np.argmin(np.abs(freqs - freq_compensated))]
    ax.plot([freq_compensated, freq_compensated], [noise_level_compensated, compensated_amplitude - circle_radius],
            color=color_signal, linewidth=3, alpha=0.8, linestyle='--')

    # 添加空心圆标记
    ax.plot(freq_compensated, compensated_amplitude, 'o', color=color_signal, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3)

    # 设置轴标签和刻度
    ax.set_xlabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=12, fontweight='bold')
    ax.set_xlim(2, 4)
    ax.set_ylim(0, 1)
    ax.set_xticks([2, 2.5, 3, 3.5, 4])

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 保存图像
    plt.tight_layout()
    plt.savefig('doppler_compensation.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

if __name__ == "__main__":
    create_single_pair_doppler_fft()
    create_compensation_fft()
    print("单对信号的多普勒频移图和补偿图都已生成！")

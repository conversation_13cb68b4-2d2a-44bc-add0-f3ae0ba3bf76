#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单个Chirp信号语谱图可视化
生成单个Chirp信号的语谱图和FFT频谱图
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from scipy import ndimage
from scipy.signal import chirp

# 设置学术风格
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.linewidth'] = 1.2

def create_single_chirp_spectrogram():
    """创建单个Chirp信号语谱图"""

    # 创建时间和频率网格（增加分辨率以获得更平滑的效果）
    time_ms = np.linspace(0, 600, 1200)  # 时间轴，0-600ms，增加分辨率
    freq_khz = np.linspace(2, 4, 800)    # 频率轴，2-4kHz，增加分辨率

    # 创建2D功率矩阵，初始化为背景功率
    power_matrix = np.full((len(freq_khz), len(time_ms)), -25.0)

    # 计算Chirp信号轨迹并设置功率
    for i, t in enumerate(time_ms):
        # 设置Chirp轨迹的功率值，增加轨迹宽度
        bandwidth = 60  # 增加轨迹宽度（频率bin数）

        # 单个Chirp信号：从(0ms, 3.8kHz)到(600ms, 2.2kHz) - 下降的chirp，从坐标轴开始
        f1 = 3.8 + (2.2 - 3.8) * t / 600

        # 只处理在[2-4]范围内的频率
        if 2 <= f1 <= 4:
            # 找到对应的频率索引
            f1_idx = int((f1 - 2) / (4 - 2) * (len(freq_khz) - 1))
            f1_idx = np.clip(f1_idx, 0, len(freq_khz) - 1)

            # Chirp信号轨迹
            for offset in range(-bandwidth//2, bandwidth//2 + 1):
                idx1 = f1_idx + offset
                if 0 <= idx1 < len(freq_khz):
                    power_matrix[idx1, i] = -15  # 中等功率

    # 应用高斯滤波进行平滑处理，消除锯齿
    power_matrix = ndimage.gaussian_filter(power_matrix, sigma=2.0)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建自定义颜色映射
    from matplotlib.colors import ListedColormap
    custom_colors = ['#fff7fb', '#ece7f2', '#d0d1e6', '#a6bddb', '#74a9cf', '#3690c0', '#0570b0', '#045a8d', '#023858']
    custom_cmap = ListedColormap(custom_colors)

    # 显示语谱图，使用自定义配色
    im = ax.imshow(power_matrix,
                   extent=[0, 600, 2, 4],  # [left, right, bottom, top]
                   aspect='auto',
                   origin='lower',  # 频率从下到上增加
                   cmap=custom_cmap,  # 使用自定义配色
                   vmin=-25, vmax=-10)

    # 设置轴标签和刻度
    ax.set_xlabel('Time (ms)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_xticks([0, 200, 400, 600])
    ax.set_yticks([2, 2.5, 3, 3.5, 4])  # 频率间隔0.5kHz

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)  # 隐藏刻度线

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('Power (dB)', fontsize=12, fontweight='bold')

    # 添加轨迹标注 - 简洁样式
    ax.text(340, 3.0, 'Chirp Signal', fontsize=12, color='black', fontweight='bold')

    # 保存图像
    plt.tight_layout()
    plt.savefig('single_chirp_spectrogram.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

def create_single_chirp_fft():
    """创建单个Chirp信号解调后的FFT频谱图"""

    # 创建FFT bin轴（高分辨率）
    n_bins = 500  # FFT bin数量
    fft_bins = np.arange(n_bins)  # FFT bin索引

    # 初始化频谱，添加微小的噪声背景
    np.random.seed(42)  # 确保结果可重现
    fft_spectrum = np.random.normal(0, 0.020, n_bins)  # 更小的噪声背景
    fft_spectrum = np.abs(fft_spectrum)  # 确保为正值

    # 信号的特征频率对应的bin：移动到140左右的位置
    peak_bin = 140  # 指定bin位置

    # 确保背景噪声保持在合适水平
    background_mask = fft_spectrum < 0.05
    fft_spectrum[background_mask] = np.clip(fft_spectrum[background_mask], 0, 0.03)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制蓝色噪声背景填充（与垂直线同色）
    ax.fill_between(fft_bins, 0, fft_spectrum, color='#1f77b4', alpha=1.0)

    # 获取指定位置的噪声背景水平
    noise_level = fft_spectrum[peak_bin]  # 该位置的噪声水平

    # 峰值高度
    peak_height = 0.3

    # 绘制从该位置噪声背景到峰值的垂直线
    ax.plot([peak_bin, peak_bin], [noise_level, peak_height],
            color='#1f77b4', linewidth=3, alpha=1, linestyle='-')

    # 设置轴标签和刻度
    ax.set_xlabel('FFT Bin', fontsize=12, fontweight='bold')
    ax.set_ylabel('|FFT|', fontsize=12, fontweight='bold')
    ax.set_xlim(0, n_bins-1)
    ax.set_ylim(0, 0.35)  # 调小纵坐标范围

    # 设置合适的刻度（适配500个bin）
    ax.set_xticks([0, 100, 200, 300, 400, 500])
    ax.set_yticks([0, 0.1, 0.2, 0.3])

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 添加峰值标注
    ax.text(peak_bin, 0.310, 'Chirp Signal', fontsize=11, color='black', fontweight='bold', ha='center')

    # 保存图像
    plt.tight_layout()
    plt.savefig('single_chirp_fft_bins.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

if __name__ == "__main__":
    create_single_chirp_spectrogram()
    create_single_chirp_fft()

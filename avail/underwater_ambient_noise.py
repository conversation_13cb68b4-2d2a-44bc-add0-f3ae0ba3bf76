import matplotlib.pyplot as plt
import numpy as np
from scipy.interpolate import interp1d
from scipy.ndimage import gaussian_filter1d

# 第一组数据（基准数据）
freq_original = [0, 0.3, 0.6, 0.65, 0.7, 0.9, 1.2, 1.5, 1.8, 2.1, 2.4, 2.7, 3, 3.3, 3.6, 3.9, 4.2, 4.5, 4.8, 5]
power_original_1 = [-24, -22, -26, -24, -28, -27, -30, -35, -33, -40, -45, -50, -53, -56, -58, -63, -70, -75, -77, -82]

# 第二组数据（轻微变化）
power_original_2 = [-25, -23, -25, -26, -29, -28, -32, -36, -34, -41, -46, -51, -52, -55, -59, -64, -71, -76, -78, -83]

# 第三组数据（包含高频成分场景）
power_original_3 = [-23, -21, -27, -23, -27, -26, -29, -34, -32, -42, -44, -49, -50, -55, -57, -62, -67, -75.5, -78, -81]



# 创建连续的频率数组
freq_continuous = np.linspace(0, 5.0, 1000)  # 1000个点，创建平滑连续曲线

# 为四组数据创建插值函数
def create_continuous_data(power_data):
    interpolator = interp1d(freq_original, power_data, kind='linear', bounds_error=False, fill_value='extrapolate')
    return interpolator(freq_continuous)

# 生成三组连续数据的基础趋势
power_base_1 = create_continuous_data(power_original_1)
power_base_2 = create_continuous_data(power_original_2)
power_base_3 = create_continuous_data(power_original_3)

# 为每组数据添加不同的随机噪声
def add_noise_to_data(power_base, seed_offset=0):
    np.random.seed(42 + seed_offset)  # 不同的随机种子

    # 创建多层次的随机变化
    main_noise = 1.5 * np.random.normal(0, 1, len(freq_continuous))
    detail_noise = 0.8 * np.sin(freq_continuous * 20 + np.random.random() * 2 * np.pi)
    medium_variation = 1.0 * np.sin(freq_continuous * 8 + np.random.random() * 2 * np.pi)

    # 组合所有噪声成分
    total_noise = main_noise + detail_noise + medium_variation

    # 对噪声进行轻微平滑
    from scipy.ndimage import gaussian_filter1d
    smoothed_noise = gaussian_filter1d(total_noise, sigma=3)

    # 将噪声添加到基础趋势上
    power_with_noise = power_base + smoothed_noise

    # 确保数据在合理范围内
    return np.clip(power_with_noise, -90, -15)

# 为三组数据添加噪声
power_continuous_1 = add_noise_to_data(power_base_1, 0)
power_continuous_2 = add_noise_to_data(power_base_2, 10)
power_continuous_3 = add_noise_to_data(power_base_3, 20)

# 创建图形
plt.figure(figsize=(10, 6))  # 增大图形尺寸以容纳三条曲线

# 获取当前轴对象
ax = plt.gca()

# 添加0-1kHz区域的背景色突出显示，限制高度到-40dB
# 使用fill_between创建有限高度的背景区域
freq_highlight = freq_continuous[(freq_continuous >= 0) & (freq_continuous <= 1)]
y_bottom = np.full_like(freq_highlight, -20)  # 底部边界
y_top = np.full_like(freq_highlight, -50)     # 顶部边界到-40dB
ax.fill_between(freq_highlight, y_bottom, y_top, alpha=0.20, color='blue', zorder=0)

# 定义三种颜色
colors = ['#e41a1c', '#377eb8', '#4daf4a']
labels = ['Location 1', 'Location 2', 'Location 3']

# 绘制三条连续曲线
plt.plot(freq_continuous, power_continuous_1, color=colors[0], linewidth=2.0, alpha=0.8, label=labels[0])
plt.plot(freq_continuous, power_continuous_2, color=colors[1], linewidth=2.0, alpha=0.8, label=labels[1])
plt.plot(freq_continuous, power_continuous_3, color=colors[2], linewidth=2.0, alpha=0.8, label=labels[2])

# 添加图例
plt.legend(loc='upper right', frameon=False, fontsize=8)

# 设置轴标签与范围
plt.xlabel('Frequency (kHz)')
plt.ylabel('Normalized Amplitude (dB)')  # 改为归一化幅度
plt.xlim(0, 5)
plt.ylim(-80, -20)

# 设置纵坐标间隔为20
plt.yticks(range(-80, -19, 20))  # -80, -60, -40, -20

# 移除刻度线，只保留数值
# plt.tick_params(axis='both', which='both', length=0, labelsize=8)

# 设置坐标轴样式（ax已经在上面定义了）
ax.spines['top'].set_visible(False)
ax.spines['right'].set_visible(False)
ax.spines['left'].set_linewidth(1)
ax.spines['bottom'].set_linewidth(1)

# 调整边距
plt.tight_layout()

# 保存图形
plt.savefig('ambient_noise.png', dpi=300, bbox_inches='tight')
print("已保存为: ambient_noise.png")
plt.close()

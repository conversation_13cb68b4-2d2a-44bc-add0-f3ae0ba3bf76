#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多人Chirp信号语谱图可视化
生成真正的语谱图：使用颜色表示功率，显示Chirp信号轨迹
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from scipy import ndimage
from scipy.signal import chirp

# 设置学术风格
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.linewidth'] = 1.2

def create_chirp_spectrogram():
    """创建Chirp信号语谱图"""

    # 创建时间和频率网格（增加分辨率以获得更平滑的效果）
    time_ms = np.linspace(0, 600, 1200)  # 时间轴，0-600ms，增加分辨率
    freq_khz = np.linspace(2, 4, 800)    # 频率轴，2-4kHz，增加分辨率

    # 创建2D功率矩阵，初始化为背景功率
    power_matrix = np.full((len(freq_khz), len(time_ms)), -25.0)

    # 计算Chirp信号轨迹并设置功率
    for i, t in enumerate(time_ms):
        # 设置Chirp轨迹的功率值，增加轨迹宽度
        bandwidth = 60  # 增加轨迹宽度（频率bin数）

        # Chirp信号1：中心频率2.7kHz，从(0ms, 2.5kHz)到(600ms, 2.9kHz) - 较小斜率
        f1 = 2.5 + (2.9 - 2.5) * t / 600

        # 只处理在[2-4]范围内的频率
        if 2 <= f1 <= 4:
            # 找到对应的频率索引
            f1_idx = int((f1 - 2) / (4 - 2) * (len(freq_khz) - 1))
            f1_idx = np.clip(f1_idx, 0, len(freq_khz) - 1)

            # Chirp信号1轨迹
            for offset in range(-bandwidth//2, bandwidth//2 + 1):
                idx1 = f1_idx + offset
                if 0 <= idx1 < len(freq_khz):
                    power_matrix[idx1, i] = -12  # 调整功率值

        # Chirp信号2：中心频率3.25kHz，从(0ms, 2.8kHz)到(600ms, 3.7kHz) - 较大斜率
        f2 = 2.8 + (3.7 - 2.8) * t / 600

        # 只处理在[2-4]范围内的频率
        if 2 <= f2 <= 4:
            f2_idx = int((f2 - 2) / (4 - 2) * (len(freq_khz) - 1))
            f2_idx = np.clip(f2_idx, 0, len(freq_khz) - 1)

            # Chirp信号2轨迹
            for offset in range(-bandwidth//2, bandwidth//2 + 1):
                idx2 = f2_idx + offset
                if 0 <= idx2 < len(freq_khz):
                    power_matrix[idx2, i] = -8   # 调整功率值

    # 应用高斯滤波进行平滑处理，消除锯齿
    power_matrix = ndimage.gaussian_filter(power_matrix, sigma=2.0)

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 创建自定义颜色映射
    from matplotlib.colors import ListedColormap
    custom_colors = ['#fff7fb', '#ece7f2', '#d0d1e6', '#a6bddb', '#74a9cf', '#3690c0', '#0570b0', '#045a8d', '#023858']
    custom_cmap = ListedColormap(custom_colors)

    # 显示语谱图，使用自定义配色
    im = ax.imshow(power_matrix,
                   extent=[0, 600, 2, 4],  # [left, right, bottom, top]
                   aspect='auto',
                   origin='lower',  # 频率从下到上增加
                   cmap=custom_cmap,  # 使用自定义配色
                   vmin=-25, vmax=-8)

    # 设置轴标签和刻度
    ax.set_xlabel('Time (ms)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_xticks([0, 200, 400, 600])
    ax.set_yticks([2, 2.5, 3, 3.5, 4])  # 频率间隔0.5kHz

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)  # 隐藏刻度线

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.8)
    cbar.set_label('Power (dB)', fontsize=12, fontweight='bold')

    # 添加轨迹标注 - 调整位置匹配新的轨迹
    ax.text(400, 2.62, 'User 1', fontsize=12, color='black', fontweight='bold')  # 中心频率2.7kHz
    ax.text(270, 3.35, 'User 2', fontsize=12, color='black', fontweight='bold')  # 中心频率3.25kHz

    # 保存图像
    plt.tight_layout()
    plt.savefig('dual_chirp_signals.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

def create_chirp_fft():
    """创建Chirp信号解调后的FFT频谱图"""

    # 创建频率轴（2-4kHz，高分辨率）
    freqs = np.linspace(2, 4, 2000)  # 高分辨率频率轴

    # 初始化频谱，添加更不规律的噪声背景
    np.random.seed(42)  # 确保结果可重现
    # 使用多种频率成分创建更不规律的噪声
    noise_base = 0.05 + np.random.normal(0, 0.008, len(freqs))
    # 添加低频波动
    low_freq_noise = 0.003 * np.sin(2 * np.pi * np.arange(len(freqs)) / 200)
    # 添加中频波动
    mid_freq_noise = 0.002 * np.sin(2 * np.pi * np.arange(len(freqs)) / 50)
    # 添加高频随机波动
    high_freq_noise = np.random.normal(0, 0.004, len(freqs))

    fft_spectrum = noise_base + low_freq_noise + mid_freq_noise + high_freq_noise
    fft_spectrum = np.abs(fft_spectrum)  # 确保为正值

    # 信号1的特征频率：2.7kHz
    peak1_freq = 2.7
    peak1_idx = np.argmin(np.abs(freqs - peak1_freq))

    # 信号2的特征频率：3.25kHz
    peak2_freq = 3.25
    peak2_idx = np.argmin(np.abs(freqs - peak2_freq))

    # 创建尖锐峰值（高斯形状，但很窄）
    peak_width = 20  # 峰值宽度（频率bin数）

    # 保存原始噪声频谱作为基础
    noise_spectrum = fft_spectrum.copy()

    # 添加信号1的尖锐峰值（在噪声基础上）
    for i in range(-peak_width//2, peak_width//2 + 1):
        idx = peak1_idx + i
        if 0 <= idx < len(freqs):
            # 高斯形状的尖锐峰值，在噪声基础上叠加
            peak_amplitude = 0.75 * np.exp(-0.5 * (i / (peak_width/6))**2)
            fft_spectrum[idx] = noise_spectrum[idx] + peak_amplitude

    # 添加信号2的尖锐峰值（在噪声基础上）
    for i in range(-peak_width//2, peak_width//2 + 1):
        idx = peak2_idx + i
        if 0 <= idx < len(freqs):
            # 高斯形状的尖锐峰值，在噪声基础上叠加
            peak_amplitude = 0.5 * np.exp(-0.5 * (i / (peak_width/6))**2)
            fft_spectrum[idx] = noise_spectrum[idx] + peak_amplitude

    # 移除背景噪声的后处理，保持峰值的连续性

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 绘制噪声背景填充
    ax.fill_between(freqs, noise_spectrum, 0, color='#2E4057', alpha=0.8)

    # 绘制噪声线（更粗的轮廓线）
    ax.plot(freqs, noise_spectrum, color='#2E4057', linewidth=2.4)

    # 绘制Signal1峰值线（从噪声基础到峰值的垂直线）
    noise_level_1 = noise_spectrum[peak1_idx]
    ax.plot([peak1_freq, peak1_freq], [noise_level_1, noise_level_1 + 0.75],
            color='#5B7C99', linewidth=3.5, linestyle='-')

    # 绘制Signal2峰值线（从噪声基础到峰值的垂直线）
    noise_level_2 = noise_spectrum[peak2_idx]
    ax.plot([peak2_freq, peak2_freq], [noise_level_2, noise_level_2 + 0.5],
            color='#5B7C99', linewidth=3.5, linestyle='-')

    # 设置轴标签和刻度
    ax.set_xlabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=12, fontweight='bold')
    ax.set_xlim(2, 4)  # 与语谱图相同的频率范围
    ax.set_ylim(0, 1)
    ax.set_xticks([2, 2.5, 3, 3.5, 4])  # 与语谱图相同的刻度

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 添加峰值标注，使用与信号色调协调的颜色
    ax.text(3.25, 0.56, 'User 2', fontsize=11, color='#5B7C99', fontweight='bold', ha='center')
    ax.text(2.7, 0.82, 'User 1', fontsize=11, color='#5B7C99', fontweight='bold', ha='center')

    # 保存图像
    plt.tight_layout()
    plt.savefig('dual_chirp_signals_fft.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

if __name__ == "__main__":
    create_chirp_spectrogram()
    create_chirp_fft()

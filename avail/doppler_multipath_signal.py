# -*- coding: utf-8 -*-
"""
多普勒频移效应演示
显示Chirp信号受多普勒效应影响的频率偏移
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from scipy import ndimage

# 设置学术风格
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.linewidth'] = 1.2

def create_doppler_fft_comparison():
    """创建多普勒频移的FFT对比图 - 六个峰值两两成对"""

    # 创建频率轴
    freqs = np.linspace(2, 4, 2000)

    # 初始化噪声背景（使用灰色）
    np.random.seed(42)
    noise_background = np.random.normal(0, 0.02, len(freqs))
    noise_background = np.abs(noise_background)

    # 定义理论值和实际值的频率（保证一致的偏移）
    freq_offset = 0.030  # 30Hz的一致偏移

    # 理论频率值
    freq_original_theory = 2.70  # 第一对信号
    freq_positive_theory = 3.0   # 第二对信号（中间）
    freq_negative_theory = 3.45  # 第三对信号

    # 实际接收频率值（都有相同的小偏移）
    freq_original_actual = freq_original_theory + freq_offset
    freq_positive_actual = freq_positive_theory + freq_offset
    freq_negative_actual = freq_negative_theory + freq_offset

    # 创建多层次的自然噪声背景
    n_points = len(freqs)

    # 1. 基础随机噪声
    base_noise = 0.04 + 0.03 * np.random.randn(n_points)

    # 2. 添加平滑的低频变化
    x = np.arange(n_points)
    low_freq_variation = 0.02 * np.sin(2 * np.pi * 0.05 * x / n_points)

    # 3. 添加中频变化
    mid_freq_variation = 0.015 * np.sin(2 * np.pi * 0.2 * x / n_points + np.pi/3)

    # 4. 添加高频细节
    high_freq_variation = 0.01 * np.sin(2 * np.pi * 0.8 * x / n_points + np.pi/6)

    # 5. 添加随机的局部变化
    local_variations = np.zeros(n_points)
    for _ in range(8):  # 8个局部变化区域
        center = np.random.randint(0, n_points)
        width = np.random.randint(30, 120)  # 变化区域宽度
        amplitude_var = np.random.uniform(0.008, 0.025)

        for i in range(max(0, center - width//2), min(n_points, center + width//2)):
            distance = abs(i - center)
            if distance < width//2:
                # 使用高斯函数创建平滑变化
                local_variations[i] += amplitude_var * np.exp(-2 * (distance / (width/2))**2)

    # 6. 组合所有噪声源
    background_spectrum = base_noise + low_freq_variation + mid_freq_variation + high_freq_variation + local_variations

    # 7. 确保非负且在合理范围内
    background_spectrum = np.maximum(background_spectrum, 0.01)
    background_spectrum = np.minimum(background_spectrum, 0.08)

    # 8. 应用平滑滤波使线条更圆滑
    background_spectrum = ndimage.gaussian_filter(background_spectrum, sigma=1.2)

    # 创建峰值函数
    def add_peak_to_spectrum(spectrum, center_freq, amplitude, width=15):
        result_spectrum = np.copy(spectrum)
        peak_idx = np.argmin(np.abs(freqs - center_freq))
        for i in range(-width//2, width//2 + 1):
            idx = peak_idx + i
            if 0 <= idx < len(freqs):
                peak_amp = amplitude * np.exp(-0.5 * (i / (width/6))**2)
                result_spectrum[idx] = max(result_spectrum[idx], peak_amp)
        return result_spectrum

    # 生成六个峰值频谱（虚线峰值比实线低一点）
    original_theory = add_peak_to_spectrum(background_spectrum, freq_original_theory, 0.6)
    original_actual = add_peak_to_spectrum(background_spectrum, freq_original_actual, 0.54)  # 比理论值低0.06

    positive_theory = add_peak_to_spectrum(background_spectrum, freq_positive_theory, 0.77)
    positive_actual = add_peak_to_spectrum(background_spectrum, freq_positive_actual, 0.69)  # 比理论值低0.08

    negative_theory = add_peak_to_spectrum(background_spectrum, freq_negative_theory, 0.36)
    negative_actual = add_peak_to_spectrum(background_spectrum, freq_negative_actual, 0.30)  # 比理论值低0.06

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 修改后的配色方案
    color_original = '#5B7C99'  # 暗钢蓝（LoS） 取消暗玫瑰色 #8B4A6B
    color_positive = '#5B7C99'  # 暗钢蓝（Reflection 1）
    color_negative = '#5B7C99'  # 暗钢蓝（Reflection 2，与Reflection 1统一）

    # 绘制噪声背景填充
    ax.fill_between(freqs, 0, background_spectrum, color='#2E4057', alpha=0.8)

    # 绘制六条垂直线表示峰值（从对应噪声点出发到峰值高度）
    # 计算圆圈标记的半径（markersize=8对应的实际半径）
    # markersize=8 在matplotlib中大约对应半径为 0.015 的视觉效果
    circle_radius = 0.010

    # 第一对 - 交换线条样式：左边虚线，右边实线
    noise_level_1_theory = background_spectrum[np.argmin(np.abs(freqs - freq_original_theory))]
    noise_level_1_actual = background_spectrum[np.argmin(np.abs(freqs - freq_original_actual))]
    ax.plot([freq_original_theory, freq_original_theory], [noise_level_1_theory, 0.6 - circle_radius],
            color=color_original, linewidth=3, alpha=0.8, linestyle='--')  # 左边虚线
    ax.plot([freq_original_actual, freq_original_actual], [noise_level_1_actual, 0.54 - circle_radius],
            color=color_original, linewidth=3, alpha=0.8, linestyle='-')  # 第一对实线（降低高度）

    # 第二对 - 交换线条样式：左边虚线，右边实线
    noise_level_2_theory = background_spectrum[np.argmin(np.abs(freqs - freq_positive_theory))]
    noise_level_2_actual = background_spectrum[np.argmin(np.abs(freqs - freq_positive_actual))]
    ax.plot([freq_positive_theory, freq_positive_theory], [noise_level_2_theory, 0.77 - circle_radius],
            color=color_positive, linewidth=3, alpha=0.8, linestyle='--')  # 左边虚线
    ax.plot([freq_positive_actual, freq_positive_actual], [noise_level_2_actual, 0.69 - circle_radius],
            color=color_positive, linewidth=3, alpha=0.8, linestyle='-')   # 第二对实线（降低高度）

    # 第三对 - 交换线条样式：左边虚线，右边实线
    noise_level_3_theory = background_spectrum[np.argmin(np.abs(freqs - freq_negative_theory))]
    noise_level_3_actual = background_spectrum[np.argmin(np.abs(freqs - freq_negative_actual))]
    ax.plot([freq_negative_theory, freq_negative_theory], [noise_level_3_theory, 0.36 - circle_radius],
            color=color_negative, linewidth=3, alpha=0.8, linestyle='--')  # 左边虚线
    ax.plot([freq_negative_actual, freq_negative_actual], [noise_level_3_actual, 0.30 - circle_radius],
            color=color_negative, linewidth=3, alpha=0.8, linestyle='-')   # 第三对实线（降低高度）

    # 在峰值位置添加彩色标记 - 交换圆圈样式
    # 理论值（空心圆圈标记，左边）
    ax.plot(freq_original_theory, 0.6, 'o', color=color_original, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3, label='Theory')
    ax.plot(freq_positive_theory, 0.77, 'o', color=color_positive, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3)
    ax.plot(freq_negative_theory, 0.36, 'o', color=color_negative, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3)

    # 实际接收值（实心圆圈标记，右边，降低高度）
    ax.plot(freq_original_actual, 0.54, 'o', color=color_original, markersize=8, alpha=0.9, label='Received')
    ax.plot(freq_positive_actual, 0.69, 'o', color=color_positive, markersize=8, alpha=0.9)
    ax.plot(freq_negative_actual, 0.30, 'o', color=color_negative, markersize=8, alpha=0.9)

    # 设置轴标签和刻度
    ax.set_xlabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=12, fontweight='bold')
    ax.set_xlim(2, 4)
    ax.set_ylim(0, 1)
    ax.set_xticks([2, 2.5, 3, 3.5, 4])

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 添加简单图例
    ax.legend(loc='upper right')

    # 添加箭头标注
    # 箭头 - LoS（独立）
    ax.annotate('LoS', xy=(freq_original_theory - 0.03, 0.6), xytext=(freq_original_theory-0.15, 0.67),
                arrowprops=dict(arrowstyle='->', color=color_original, lw=2),
                fontsize=11, color=color_original, fontweight='bold', ha='center')

    # 添加Reflection标注 - 统一标注第二对和第三对峰值
    reflection_text_x = 3.30  # 在3.0-3.5 kHz之间的中心位置
    reflection_text_y = 0.55  # 适中的高度，避免与其他元素冲突

    # Reflection文本标注
    ax.text(reflection_text_x, reflection_text_y, 'Reflection',
            ha='center', va='center', fontsize=11, color=color_positive, fontweight='bold')

    # 左上方向箭头
    left_shift = 0.02  # 左上方向的平移量
    # 指向实线位置附近，但保持距离，避开峰值标注区域
    target_x = freq_positive_actual + 0.06  # 实线侧一点距离
    target_y = 0.65  # 实线中上部高度，避开峰值和标注
    ax.annotate('', xy=(target_x, target_y),
                xytext=(reflection_text_x - left_shift, reflection_text_y + left_shift),
                arrowprops=dict(arrowstyle='->', color=color_positive, lw=2))

    # 右下方向箭头
    right_shift = 0.02  # 右下方向的平移量
    ax.annotate('', xy=(3.348 + right_shift, 0.452 - right_shift),
                xytext=(reflection_text_x + right_shift, reflection_text_y - right_shift),
                arrowprops=dict(arrowstyle='->', color=color_negative, lw=2))

    # 添加多普勒频移标注 - 只在第二对峰值添加双向箭头显示频移距离
    doppler_color = '#D32F2F'  # 使用红色，更加醒目

    # 第二对峰值的频移箭头（在理论峰值高度的55%处）
    arrow_height_2 = 0.77 * 0.55
    ax.annotate('', xy=(freq_positive_actual, arrow_height_2),
                xytext=(freq_positive_theory, arrow_height_2),
                arrowprops=dict(arrowstyle='<->', color=doppler_color, lw=1.5,
                               shrinkA=0, shrinkB=0, mutation_scale=5)) # mutation_scale控制箭头大小，lw控制箭头粗细

    # 频移值标注 - 放在0.40位置
    ax.text(freq_positive_actual + 0.02, 0.40, f'δf',
            ha='left', va='center', fontsize=14, color=doppler_color, fontweight='bold')

    # 添加第二对峰值差距值标注 - 工程制图样式
    peak_diff_color = '#D32F2F'  # 使用红色

    # 以实线所在位置为基准，在其右侧绘制尺寸标注
    annotation_x = freq_positive_actual + 0.03

    # 上横线（理论值高度）
    ax.plot([annotation_x - 0.005, annotation_x + 0.005], [0.77, 0.77],
            color=peak_diff_color, linewidth=2, alpha=0.8)

    # 下横线（实际值高度）
    ax.plot([annotation_x - 0.005, annotation_x + 0.005], [0.70, 0.70],
            color=peak_diff_color, linewidth=2, alpha=0.8)

    # 中间竖线
    ax.plot([annotation_x , annotation_x], [0.70, 0.77],
            color=peak_diff_color, linewidth=2, alpha=0.8)

    # 峰值差距值标注 - 放在尺寸标注右侧，加粗
    ax.text(annotation_x + 0.02, (0.77 + 0.70) / 2, f'Δh',
            ha='left', va='center', fontsize=14, color=peak_diff_color, fontweight='bold')

    # 保存图像
    plt.tight_layout()
    plt.savefig('doppler_multipath_origin.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig

def create_simplified_fft_comparison():
    """创建补偿图"""

    # 创建频率轴
    freqs = np.linspace(2, 4, 2000)

    # 初始化噪声背景（使用灰色）
    np.random.seed(42)
    noise_background = np.random.normal(0, 0.02, len(freqs))
    noise_background = np.abs(noise_background)

    # 定义理论值和实际值的频率（保证一致的偏移）
    freq_offset = 0.030  # 30Hz的一致偏移

    # 理论频率值
    freq_original_theory = 2.70  # 第一对信号
    freq_positive_theory = 3.0   # 第二对信号（中间）
    freq_negative_theory = 3.45  # 第三对信号

    # 实际接收频率值（都有相同的小偏移）
    freq_original_actual = freq_original_theory + freq_offset
    freq_positive_actual = freq_positive_theory + freq_offset
    freq_negative_actual = freq_negative_theory + freq_offset

    # 创建多层次的自然噪声背景
    n_points = len(freqs)

    # 1. 基础随机噪声
    base_noise = 0.04 + 0.03 * np.random.randn(n_points)

    # 2. 添加平滑的低频变化
    x = np.arange(n_points)
    low_freq_variation = 0.02 * np.sin(2 * np.pi * 0.05 * x / n_points)

    # 3. 添加中频变化
    mid_freq_variation = 0.015 * np.sin(2 * np.pi * 0.2 * x / n_points + np.pi/3)

    # 4. 添加高频细节
    high_freq_variation = 0.01 * np.sin(2 * np.pi * 0.8 * x / n_points + np.pi/6)

    # 5. 添加随机的局部变化
    local_variations = np.zeros(n_points)
    for _ in range(8):  # 8个局部变化区域
        center = np.random.randint(0, n_points)
        width = np.random.randint(30, 120)  # 变化区域宽度
        amplitude_var = np.random.uniform(0.008, 0.025)

        for i in range(max(0, center - width//2), min(n_points, center + width//2)):
            distance = abs(i - center)
            if distance < width//2:
                # 使用高斯函数创建平滑变化
                local_variations[i] += amplitude_var * np.exp(-2 * (distance / (width/2))**2)

    # 6. 组合所有噪声源
    background_spectrum = base_noise + low_freq_variation + mid_freq_variation + high_freq_variation + local_variations

    # 7. 确保非负且在合理范围内
    background_spectrum = np.maximum(background_spectrum, 0.01)
    background_spectrum = np.minimum(background_spectrum, 0.08)

    # 8. 应用平滑滤波使线条更圆滑
    background_spectrum = ndimage.gaussian_filter(background_spectrum, sigma=1.2)

    # 创建峰值函数
    def add_peak_to_spectrum(spectrum, center_freq, amplitude, width=15):
        result_spectrum = np.copy(spectrum)
        peak_idx = np.argmin(np.abs(freqs - center_freq))
        for i in range(-width//2, width//2 + 1):
            idx = peak_idx + i
            if 0 <= idx < len(freqs):
                peak_amp = amplitude * np.exp(-0.5 * (i / (width/6))**2)
                result_spectrum[idx] = max(result_spectrum[idx], peak_amp)
        return result_spectrum

    # 生成峰值频谱（虚线高度和实线高度）
    # 虚线高度（理论值）
    original_theory_height = 0.6
    positive_theory_height = 0.77
    negative_theory_height = 0.36

    # 实线高度（稍微低一点，但圆圈几乎相切）
    height_offset = 0.02  # 实线比虚线低0.03
    circle_offset = 0.020  # 圆圈相切效果，比线条高度差小一些
    original_actual_height = original_theory_height - height_offset
    positive_actual_height = positive_theory_height - height_offset
    negative_actual_height = negative_theory_height - height_offset

    # 实心圆圈的高度（几乎相切）
    original_actual_circle_height = original_theory_height - circle_offset
    positive_actual_circle_height = positive_theory_height - circle_offset
    negative_actual_circle_height = negative_theory_height - circle_offset

    # 创建图形
    fig, ax = plt.subplots(figsize=(12, 8))

    # 修改后的配色方案
    color_original = '#5B7C99'  # 暗钢蓝（LoS）
    color_positive = '#5B7C99'  # 暗钢蓝（Reflection 1）
    color_negative = '#5B7C99'  # 暗钢蓝（Reflection 2，与Reflection 1统一）

    # 绘制噪声背景填充
    ax.fill_between(freqs, 0, background_spectrum, color='#2E4057', alpha=0.8)

    # 绘制实线和虚线混合效果 - 在同一频率位置
    circle_radius = 0.010

    # 第一对 - 虚线（较高）+ 实线（较低）
    noise_level_1 = background_spectrum[np.argmin(np.abs(freqs - freq_original_theory))]
    # 虚线（理论值，较高）
    ax.plot([freq_original_theory, freq_original_theory], [noise_level_1, original_theory_height - circle_radius],
            color=color_original, linewidth=3, alpha=0.8, linestyle='--')
    # 实线（实际值，较低）
    ax.plot([freq_original_theory, freq_original_theory], [noise_level_1, original_actual_height - circle_radius],
            color=color_original, linewidth=3, alpha=0.8, linestyle='-')

    # 第二对 - 虚线（较高）+ 实线（较低）
    noise_level_2 = background_spectrum[np.argmin(np.abs(freqs - freq_positive_theory))]
    # 虚线（理论值，较高）
    ax.plot([freq_positive_theory, freq_positive_theory], [noise_level_2, positive_theory_height - circle_radius],
            color=color_positive, linewidth=3, alpha=0.8, linestyle='--')
    # 实线（实际值，较低）
    ax.plot([freq_positive_theory, freq_positive_theory], [noise_level_2, positive_actual_height - circle_radius],
            color=color_positive, linewidth=3, alpha=0.8, linestyle='-')

    # 第三对 - 虚线（较高）+ 实线（较低）
    noise_level_3 = background_spectrum[np.argmin(np.abs(freqs - freq_negative_theory))]
    # 虚线（理论值，较高）
    ax.plot([freq_negative_theory, freq_negative_theory], [noise_level_3, negative_theory_height - circle_radius],
            color=color_negative, linewidth=3, alpha=0.8, linestyle='--')
    # 实线（实际值，较低）
    ax.plot([freq_negative_theory, freq_negative_theory], [noise_level_3, negative_actual_height - circle_radius],
            color=color_negative, linewidth=3, alpha=0.8, linestyle='-')

    # 在峰值位置添加圆形标记 - 虚线用空心圆，实线用实心圆
    # 虚线峰值 - 空心圆标记（较高位置）
    ax.plot(freq_original_theory, original_theory_height, 'o', color=color_original, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3, label='Theory')
    ax.plot(freq_positive_theory, positive_theory_height, 'o', color=color_positive, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3)
    ax.plot(freq_negative_theory, negative_theory_height, 'o', color=color_negative, markersize=8, alpha=0.9,
            fillstyle='full', markerfacecolor='white', markeredgewidth=2, zorder=3)

    # 实线峰值 - 实心圆标记（几乎相切的位置）
    ax.plot(freq_original_theory, original_actual_circle_height, 'o', color=color_original, markersize=8, alpha=0.9, zorder=4, label='Received')
    ax.plot(freq_positive_theory, positive_actual_circle_height, 'o', color=color_positive, markersize=8, alpha=0.9, zorder=4)
    ax.plot(freq_negative_theory, negative_actual_circle_height, 'o', color=color_negative, markersize=8, alpha=0.9, zorder=4)

    # 设置轴标签和刻度
    ax.set_xlabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=12, fontweight='bold')
    ax.set_xlim(2, 4)
    ax.set_ylim(0, 1)
    ax.set_xticks([2, 2.5, 3, 3.5, 4])

    # 去掉网格线和刻度线
    ax.grid(False)
    ax.tick_params(which='both', length=0)

    # 添加图例
    ax.legend(loc='upper right')

    # 保存图像
    plt.tight_layout()
    plt.savefig('doppler_multipath_compensation.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    return fig


if __name__ == "__main__":
    create_doppler_fft_comparison()
    create_simplified_fft_comparison()
    print("原始图和补偿图都已生成！")

#!/usr/bin/env python3
"""
频域信号可视化脚本
根据python-data-analysis.md规则生成符合学术标准的频域图表
"""

import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Tuple
from scipy import ndimage

# 设置学术风格
plt.style.use('seaborn-v0_8-whitegrid')

def generate_frequency_domain_signal(n_points: int = 2000, freq_range: Tuple[float, float] = (1.0, 4.0)) -> Tuple[np.ndarray, np.ndarray]:
    """
    生成频域信号数据

    Args:
        n_points: 数据点数量
        freq_range: 频率范围 (kHz)

    Returns:
        Tuple[np.ndarray, np.ndarray]: 频率序列和幅度信号
    """
    # 生成频率序列 (kHz)
    freq = np.linspace(freq_range[0], freq_range[1], n_points)

    # 设置随机种子以便重现
    np.random.seed(42)

    # 创建更自然、变化的噪声基线
    # 基础随机噪声
    base_noise = 0.04 + 0.06 * np.random.randn(n_points)

    # 添加平滑的低频变化
    x = np.arange(n_points)
    low_freq_variation = 0.03 * np.sin(2 * np.pi * 0.05 * x / n_points)

    # 添加中频变化
    mid_freq_variation = 0.02 * np.sin(2 * np.pi * 0.2 * x / n_points + np.pi/3)

    # 添加高频细节
    high_freq_variation = 0.015 * np.sin(2 * np.pi * 0.8 * x / n_points + np.pi/6)

    # 添加随机的局部变化
    local_variations = np.zeros(n_points)
    for _ in range(15):  # 15个局部变化区域
        center = np.random.randint(0, n_points)
        width = np.random.randint(50, 200)  # 更宽的变化区域
        amplitude_var = np.random.uniform(0.01, 0.04)

        for i in range(max(0, center - width//2), min(n_points, center + width//2)):
            distance = abs(i - center)
            if distance < width//2:
                # 使用高斯函数创建平滑变化
                local_variations[i] += amplitude_var * np.exp(-2 * (distance / (width/2))**2)

    # 组合所有噪声源
    amplitude = base_noise + low_freq_variation + mid_freq_variation + high_freq_variation + local_variations

    # 确保非负且不超过0.15
    amplitude = np.maximum(amplitude, 0)
    amplitude = np.minimum(amplitude, 0.15)

    # 应用平滑滤波使线条更圆滑
    amplitude = ndimage.gaussian_filter1d(amplitude, sigma=1.5)
    
    # 选择脉冲位置（在2.5-3.5 kHz之间）
    pulse_freq = 2.8  # kHz，选择2.8 kHz作为脉冲中心
    pulse_idx = np.argmin(np.abs(freq - pulse_freq))
    
    # 创建两级尖锐脉冲
    pulse_width = 2  # 极其尖锐，宽度为2个数据点
    
    # 第一级：幅度0.6
    for offset in range(-pulse_width, pulse_width + 1):
        idx = pulse_idx + offset
        if 0 <= idx < n_points:
            if offset == 0:
                # 中心点
                amplitude[idx] = 0.6
            elif abs(offset) == 1:
                # 相邻点
                amplitude[idx] = max(amplitude[idx], 0.6 * 0.8)
            elif abs(offset) == 2:
                # 边缘点
                amplitude[idx] = max(amplitude[idx], 0.6 * 0.3)
    
    # 第二级：在第一级基础上继续上升到0.85
    # 第二级更窄，只在中心点
    amplitude[pulse_idx] = 0.85
    
    # 第二级的相邻点稍微影响
    for offset in [-1, 1]:
        idx = pulse_idx + offset
        if 0 <= idx < n_points:
            amplitude[idx] = max(amplitude[idx], 0.75)
    
    return freq, amplitude

def create_frequency_domain_plot(freq: np.ndarray, amplitude: np.ndarray) -> plt.Figure:
    """
    创建频域信号图表
    
    Args:
        freq: 频率序列
        amplitude: 幅度序列
        
    Returns:
        plt.Figure: 生成的图表
    """
    # 创建图表，使用学术标准尺寸
    fig, ax = plt.subplots(figsize=(12, 8), dpi=300)
    
    # 使用统一的深蓝色
    color = '#2E4057'
    
    # 绘制尖锐脉冲信号（增加线条粗细）
    ax.plot(freq, amplitude, color=color, linewidth=2.5, alpha=0.9)

    # 为噪声部分添加背景填充
    ax.fill_between(freq, 0, amplitude, color=color, alpha=0.15)

    # 添加峰值标记
    pulse_idx = np.argmax(amplitude)
    pulse_freq = freq[pulse_idx]

    # 找到0.6和0.85幅度位置
    level1_indices = np.where((amplitude >= 0.55) & (amplitude <= 0.70))[0]  # 扩大搜索范围
    level2_idx = pulse_idx  # 0.85位置就是峰值位置

    # 标记0.6位置（Before）
    ax.scatter(pulse_freq, 0.6, marker='o', s=120,
              color='#0050EF', edgecolor='#5D5347', linewidth=2,
              label='Before', zorder=5)

    # 标记0.85位置（After）
    ax.scatter(pulse_freq, amplitude[level2_idx], marker='o', s=120,
              color='#FF1807', edgecolor='#4F5F5F', linewidth=2,
              label='After', zorder=5)

    # 设置坐标轴标签 (英文，符合IEEE标准)
    ax.set_xlabel('Frequency (kHz)', fontsize=14, fontweight='bold')
    ax.set_ylabel('Normalized Amplitude', fontsize=14, fontweight='bold')

    # 设置坐标轴范围和刻度
    ax.set_xlim(freq.min(), freq.max())
    ax.set_ylim(0, 1.0)  # Y轴范围0-1

    # 设置X轴刻度为1的间隔
    ax.set_xticks([1, 2, 3, 4])

    # 设置Y轴刻度间隔为0.2
    ax.set_yticks([0, 0.2, 0.4, 0.6, 0.8, 1.0])
    
    # 去掉网格
    ax.grid(False)

    # 设置刻度
    ax.tick_params(axis='both', which='major', labelsize=12)

    # 添加零线
    ax.axhline(y=0, color='black', linewidth=0.8, alpha=0.8)

    # 设置背景色为纯白
    ax.set_facecolor('white')

    # 添加简洁的平面图例（位于右侧）
    ax.legend(loc='upper right', fontsize=12, frameon=True,
             fancybox=False, shadow=False, framealpha=1.0,
             edgecolor='black', facecolor='white')

    return fig

def add_amplitude_annotations(ax: plt.Axes, freq: np.ndarray, amplitude: np.ndarray):
    """
    添加幅度标注（现在只是一个占位函数，实际标记在主绘图函数中完成）

    Args:
        ax: matplotlib轴对象
        freq: 频率序列
        amplitude: 幅度序列
    """
    # 移除了文本说明框和箭头标注
    # 现在使用符号标记和图例来显示信息
    pass

def main():
    """
    主函数：生成并保存频域信号图表
    """
    print("正在生成频域信号图表...")
    
    # 生成频域数据
    freq, amplitude = generate_frequency_domain_signal(n_points=2000, freq_range=(1.0, 4.0))
    
    # 创建图表
    fig = create_frequency_domain_plot(freq, amplitude)
    
    # 添加幅度标注
    add_amplitude_annotations(fig.gca(), freq, amplitude)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表 (PNG格式，符合规则要求)
    output_filename = 'multipath_aggregation.png'
    fig.savefig(output_filename, dpi=300, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    print(f"图表已保存为: {output_filename}")
    
    # 打印数据摘要
    print("\n数据摘要:")
    print(f"频率范围: {freq.min():.1f} - {freq.max():.1f} kHz")
    print(f"数据点数量: {len(freq)}")
    print(f"最大幅度: {amplitude.max():.2f}")
    print(f"脉冲位置: {freq[np.argmax(amplitude)]:.2f} kHz")
    print(f"基线噪声水平: {np.mean(amplitude[amplitude < 0.2]):.3f}")
    print(f"噪声范围: {np.min(amplitude[amplitude < 0.2]):.3f} - {np.max(amplitude[amplitude < 0.2]):.3f}")

if __name__ == "__main__":
    main()

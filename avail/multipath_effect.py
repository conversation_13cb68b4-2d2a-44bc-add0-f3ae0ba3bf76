#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水下多径效应时间域相关系数图
Underwater Multipath Effect - Time Domain Correlation Coefficient

显示水下声学通信中多径效应的时间域特性
横轴：时间(ms) 0-60
纵轴：归一化相关系数 0-1，间隔0.2
"""

import matplotlib.pyplot as plt
import numpy as np

# 设置中文字体和学术风格
plt.rcParams['font.family'] = ['Arial', 'DejaVu Sans']
plt.rcParams['font.size'] = 12
plt.rcParams['axes.linewidth'] = 1.0
plt.rcParams['grid.alpha'] = 0.3

def create_underwater_multipath_correlation():
    """创建水下多径效应时间域相关系数图"""

    # 创建图形和坐标轴
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    # 设置时间轴 0-60ms
    time = np.linspace(0, 60, 1000)

    # 定义颜色
    curve_color = '#1f4e79'       # 统一的曲线颜色
    los_color = '#000000'         # 黑色用于LoS标注
    reflection_color = '#D32F2F'  # 红色用于反射标注

    # 初始化相关系数信号
    correlation = np.zeros(len(time))

    # 1. 直达路径 (LoS) - 最早到达，相关系数最高
    los_time = 8.0  # ms
    los_amplitude = 1.0
    los_width = 1.5
    los_signal = los_amplitude * np.exp(-0.5 * ((time - los_time) / los_width) ** 2)
    correlation += los_signal

    # 2. 海面反射路径 - 稍后到达
    surface_time = 15.0  # ms
    surface_amplitude = 0.7
    surface_width = 2.0
    surface_signal = surface_amplitude * np.exp(-0.5 * ((time - surface_time) / surface_width) ** 2)
    correlation += surface_signal

    # 3. 海底反射路径 - 更晚到达
    bottom_time = 25.0  # ms
    bottom_amplitude = 0.5
    bottom_width = 2.5
    bottom_signal = bottom_amplitude * np.exp(-0.5 * ((time - bottom_time) / bottom_width) ** 2)
    correlation += bottom_signal

    # 4. 多次反射路径 - 最晚到达，幅度最小
    multi1_time = 35.0  # ms
    multi1_amplitude = 0.3
    multi1_width = 3.0
    multi1_signal = multi1_amplitude * np.exp(-0.5 * ((time - multi1_time) / multi1_width) ** 2)
    correlation += multi1_signal

    multi2_time = 45.0  # ms
    multi2_amplitude = 0.2
    multi2_width = 3.5
    multi2_signal = multi2_amplitude * np.exp(-0.5 * ((time - multi2_time) / multi2_width) ** 2)
    correlation += multi2_signal

    # 添加多样化的噪声和不规则性，使曲线更真实
    np.random.seed(42)  # 确保结果可重现

    # 1. 基础高斯白噪声
    white_noise = np.random.normal(0, 0.015, len(time))

    # 2. 低频漂移噪声
    drift_noise = 0.008 * np.sin(time * 0.1 + np.random.random()) * np.cos(time * 0.05)

    # 3. 脉冲噪声（偶尔的尖峰）
    pulse_noise = np.zeros(len(time))
    pulse_positions = np.random.choice(len(time), size=int(len(time)*0.02), replace=False)
    pulse_noise[pulse_positions] = np.random.normal(0, 0.03, len(pulse_positions))

    # 4. 相关噪声（模拟测量系统的系统性误差）
    correlated_noise = np.zeros(len(time))
    for i in range(1, len(time)):
        correlated_noise[i] = 0.7 * correlated_noise[i-1] + 0.3 * np.random.normal(0, 0.01)

    # 5. 频率相关的噪声
    freq_noise = 0.005 * np.sin(time * 0.8 + np.random.random() * 2 * np.pi) * np.random.normal(1, 0.2, len(time))

    # 6. 幅度相关的噪声（在峰值附近噪声稍大）
    amplitude_noise = 0.01 * correlation * np.random.normal(0, 1, len(time))

    # 合成所有噪声
    total_noise = white_noise + drift_noise + pulse_noise + correlated_noise + freq_noise + amplitude_noise

    # 合成最终信号
    correlation += total_noise

    # 确保相关系数在0-1范围内
    correlation = np.clip(correlation, 0, 1)

    # 绘制连续的相关系数曲线
    ax.plot(time, correlation, color=curve_color, linewidth=2.0)

    # 对曲线下面做填充
    ax.fill_between(time, 0, correlation, color=curve_color, alpha=0.5)

    # 添加LoS标注 - 黑色箭头从(12,0.82)指向(9,0.96)
    ax.annotate('LoS', xy=(9, 0.96), xytext=(12, 0.82),
                arrowprops=dict(arrowstyle='->', color='black', lw=2),
                fontsize=14, color='black', weight='bold')

    # ========== Reflection标注 - 分开管理文本和箭头 ==========
    reflection_color = '#FF1807'  # 红色

    # 1. Reflection文本标注
    # 文本位置：精确坐标(22.67, 0.780)
    reflection_text_x = 22.67
    reflection_text_y = 0.780
    ax.text(reflection_text_x, reflection_text_y, 'Reflection',
            ha='center', va='center', fontsize=14,
            color=reflection_color, fontweight='bold')

    # 2. 第一条Reflection箭头 - 指向第二个峰值(15ms处)
    # 箭头起点：文本正下方0.05处 (22.67, 0.765)
    arrow1_start_x = 22.67
    arrow1_start_y = 0.765  # 0.815 - 0.05 = 0.765
    # 箭头终点：第二个峰值附近，不直接接触峰值
    arrow1_end_x = 16.6    # 第二个峰值的时间位置
    arrow1_end_y = 0.72     # 距离峰值保持适当距离的y坐标
    ax.annotate('',
               xy=(arrow1_end_x, arrow1_end_y),           # 箭头终点
               xytext=(arrow1_start_x, arrow1_start_y),   # 箭头起点
               arrowprops=dict(arrowstyle='->', color=reflection_color, lw=2))

    # 3. 第二条Reflection箭头 - 指向第三个峰值(25ms处)
    # 箭头起点：与第一条箭头相同的起点 (22.67, 0.765)
    arrow2_start_x = 22.67
    arrow2_start_y = 0.765  # 与第一条箭头共享起点
    # 箭头终点：第三个峰值附近，不直接接触峰值
    arrow2_end_x = 24.2     # 第三个峰值的时间位置
    arrow2_end_y = 0.55    # 距离峰值保持适当距离的y坐标
    ax.annotate('',
               xy=(arrow2_end_x, arrow2_end_y),           # 箭头终点
               xytext=(arrow2_start_x, arrow2_start_y),   # 箭头起点
               arrowprops=dict(arrowstyle='->', color=reflection_color, lw=2))
    
    # 设置坐标轴
    ax.set_xlim(0, 60)
    ax.set_ylim(0, 1)

    # 设置坐标轴标签
    ax.set_xlabel('Time (ms)', fontsize=12, weight='bold')
    ax.set_ylabel('Normalized Correlation', fontsize=12, weight='bold')

    # 设置y轴刻度，间隔0.2
    ax.set_yticks(np.arange(0, 1.1, 0.2))
    ax.set_yticklabels(['0.0', '0.2', '0.4', '0.6', '0.8', '1.0'])

    # 移除刻度线，只保留数值
    ax.tick_params(axis='both', which='both', length=0)

    # 设置背景色为白色
    ax.set_facecolor('white')

    # 移除网格
    ax.grid(False)

    # 移除边框
    for spine in ax.spines.values():
        spine.set_visible(False)

    # 只保留左边和底边的坐标轴
    ax.spines['left'].set_visible(True)
    ax.spines['bottom'].set_visible(True)
    ax.spines['left'].set_linewidth(1.0)
    ax.spines['bottom'].set_linewidth(1.0)

    # 调整布局
    plt.tight_layout()

    # 保存图片
    plt.savefig('multipath_effect.png', dpi=300, bbox_inches='tight',
                facecolor='white', edgecolor='none')

    print("水下多径效应时间域相关系数图已生成并保存为:")
    print("- underwater_multipath_correlation.png")

    return fig, ax

if __name__ == "__main__":
    # 创建图表
    fig, ax = create_underwater_multipath_correlation()
    print("图表创建完成！")

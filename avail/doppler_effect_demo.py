#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多普勒效应下的Chirp信号可视化演示
展示原始信号和多普勒频移后的信号对比
"""

import matplotlib.pyplot as plt
import numpy as np
import matplotlib
from scipy import ndimage

# 设置学术风格
matplotlib.rcParams['font.size'] = 12
matplotlib.rcParams['axes.linewidth'] = 1.2


def create_doppler_chirp_spectrogram():
    """
    创建并保存一个带有精确箭头标注的多普勒效应下的Chirp信号语谱图。
    该版本精确控制了指示箭头的起止位置，使其位于信号轨迹的内侧。
    """

    # --- 1. 创建时间和频率网格 ---
    # 调整频率范围以容纳上行频移信号
    time_ms = np.linspace(0, 600, 1200)  # 时间轴，0-600ms
    freq_khz = np.linspace(2, 4, 800)  # 频率轴，2-4kHz

    # --- 2. 创建2D功率矩阵 ---
    # 初始化为背景功率
    power_matrix = np.full((len(freq_khz), len(time_ms)), -25.0)

    # 定义信号轨迹的功率值
    colors = {
        'original': -15,  # 原始信号
        'positive': -12,  # 正频移信号 (更亮)
    }

    # 定义频移参数
    doppler_shift_positive = 0.3  # 正频移（上行） in kHz，调整以适应新的频率范围

    # --- 3. 计算并绘制Chirp信号轨迹 ---
    for i, t in enumerate(time_ms):
        bandwidth = 50  # 轨迹在频率轴上的宽度（以索引计）

        # 原始Chirp信号：从(0ms, 3.3kHz)到(600ms, 2.1kHz) - 向下平移以便箭头起点在内侧线
        f_original = 3.3 + (2.1 - 3.3) * t / 600

        # 正频移Chirp信号（上行多普勒）
        f_positive = f_original + doppler_shift_positive

        # 将两条轨迹的信息整合
        frequencies = [
            (f_original, colors['original']),
            (f_positive, colors['positive'])
        ]

        # 在功率矩阵中设置轨迹的功率
        for freq, power in frequencies:
            if 2 <= freq <= 4:
                # 找到对应的频率索引
                freq_idx = np.interp(freq, freq_khz, np.arange(len(freq_khz)))
                freq_idx = int(round(freq_idx))

                # 设置轨迹功率，使其具有一定宽度
                start_idx = max(0, freq_idx - bandwidth // 2)
                end_idx = min(len(freq_khz), freq_idx + bandwidth // 2 + 1)
                power_matrix[start_idx:end_idx, i] = power

    # --- 4. 应用高斯滤波进行平滑处理 ---
    power_matrix = ndimage.gaussian_filter(power_matrix, sigma=2.0)

    # --- 5. 创建图形和语谱图 ---
    fig, ax = plt.subplots(figsize=(12, 8), facecolor='white')

    # 显示语谱图，使用蓝色系同系色配色
    im = ax.imshow(power_matrix,
                   extent=[time_ms[0], time_ms[-1], freq_khz[0], freq_khz[-1]],
                   aspect='auto',
                   origin='lower',
                   cmap='Blues',
                   vmin=-25, vmax=-10)

    # --- 6. 设置轴标签、刻度和标题 ---
    ax.set_xlabel('Time (ms)', fontsize=12, fontweight='bold')
    ax.set_ylabel('Frequency (kHz)', fontsize=12, fontweight='bold')
    ax.set_xticks([0, 200, 400, 600])
    ax.set_yticks([2.0, 2.5, 3.0, 3.5, 4.0])

    # 去掉网格线和刻度线（包括左右两侧）
    ax.grid(False)
    ax.tick_params(which='both', length=0)  # 隐藏左侧刻度线
    ax.tick_params(axis='y', which='both', right=False)  # 隐藏右侧刻度线
    ax.yaxis.set_ticks_position('left')  # 只在左侧显示刻度标签

    # 添加颜色条
    cbar = plt.colorbar(im, ax=ax, shrink=0.85, pad=0.02)
    cbar.set_label('Power (dB)', fontsize=12, fontweight='bold')

    # --- 7. 添加竖直的频移箭头和标注 ---
    t_arrow = 300  # 箭头位置的时间点

    # 计算该时间点两条信号中心线的频率
    f_orig_center = 3.3 + (2.1 - 3.3) * t_arrow / 600
    f_shift_center = f_orig_center + doppler_shift_positive

    # 计算轨迹宽度对应的频率偏移（从像素宽度转换为频率宽度）
    freq_range = 4 - 2  # 总频率范围
    freq_per_pixel = freq_range / 800  # 每个像素对应的频率
    track_width_freq = (bandwidth / 2) * freq_per_pixel  # 轨迹半宽度对应的频率

    # 箭头起点：原始信号的上内侧边缘
    f_arrow_start = f_orig_center + track_width_freq * 0.8  # 稍微内侧一点

    # 箭头终点：频移信号的下内侧边缘
    f_arrow_end = f_shift_center - track_width_freq * 0.8  # 稍微内侧一点

    # 绘制竖直的虚线箭头（从原始信号内侧指向频移信号内侧）
    ax.annotate('',
                xy=(t_arrow, f_arrow_end),
                xytext=(t_arrow, f_arrow_start),
                arrowprops=dict(arrowstyle='->', linestyle='--', color='black', lw=2.5))

    # 在箭头左侧偏下位置添加Δf符号标注，加大字体并加粗
    f_label = (f_arrow_start + f_arrow_end) / 2 + 0.03  # 向下偏移
    ax.text(t_arrow - 20, f_label, r'$\Delta f$', fontsize=20, color='black',
            fontweight='bold', ha='center', va='center')

    # 添加信号标注 - 调整位置以适应新的信号位置
    ax.text(240, 2.54, 'Original Signal', fontsize=11, color='black', fontweight='bold')
    ax.text(280, 3.15, 'Doppler Shifted', fontsize=11, color='black', fontweight='bold')

    # --- 8. 保存图像 ---
    plt.tight_layout(rect=[0, 0, 1, 0.96])  # 调整布局以适应标题
    plt.savefig('doppler_chirp_spectrogram_final.png', dpi=300, bbox_inches='tight')

if __name__ == "__main__":
    create_doppler_chirp_spectrogram()
    print("多普勒效应演示完成！")

#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m'

# 全局变量
APP_NAME=""
SELECTED_APPS=()  # 用户选择的应用列表
found_files=()
exclude_patterns=()
search_patterns=()
DRY_RUN=false
BACKUP_DIR=""
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="$HOME/.app_cleaner.log"

# 安全性增强变量
DELETED_FILES_STACK=()  # 用于回滚的已删除文件栈
BACKUP_MANIFEST=""      # 备份清单文件

# 默认配置变量
MAX_SEARCH_DEPTH=3  # 浅度搜索，提高性能
ENABLE_BACKUP=true
BACKUP_RETENTION_DAYS=180  # 改为半年
REQUIRE_CONFIRMATION=true
PROTECT_SYSTEM_FILES=true
FAST_SEARCH=false   # 快速搜索模式

# 安全白名单：允许删除的路径前缀
SAFE_DELETE_PATHS=(
    "/Applications/"
    "$HOME/Library/Application Support/"
    "$HOME/Library/Preferences/"
    "$HOME/Library/Caches/"
    "$HOME/Library/Logs/"
    "$HOME/Library/WebKit/"
    "$HOME/Library/HTTPStorages/"
    "$HOME/Library/Application Scripts/"
    "$HOME/Library/Containers/"
    "$HOME/Library/Group Containers/"
    "$HOME/Library/Saved Application State/"
    "$HOME/Library/LaunchAgents/"
)

# 危险路径黑名单：绝对不允许删除的路径
DANGEROUS_PATHS=(
    "/System/"
    "/usr/bin/"
    "/usr/sbin/"
    "/bin/"
    "/sbin/"
    "/Library/Apple/"
    "/Library/System/"
    "/Applications/Utilities/"
    "/Applications/System Preferences.app"
    "/Applications/Finder.app"
    "/Applications/Safari.app"
    "/Applications/Mail.app"
    "/Applications/Messages.app"
    "/Applications/FaceTime.app"
    "/Applications/Photos.app"
    "/Applications/Music.app"
    "/Applications/TV.app"
    "/Applications/Podcasts.app"
    "/Applications/Books.app"
    "/Applications/App Store.app"
)

# 解析命令行参数
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --depth)
                if [[ "$2" =~ ^[0-9]+$ ]] && [[ "$2" -ge 1 ]] && [[ "$2" -le 10 ]]; then
                    MAX_SEARCH_DEPTH="$2"
                    shift 2
                else
                    echo "错误: --depth 参数必须是 1-10 之间的数字"
                    exit 1
                fi
                ;;
            --no-backup)
                ENABLE_BACKUP=false
                shift
                ;;
            --backup-days)
                if [[ "$2" =~ ^[0-9]+$ ]] && [[ "$2" -ge 1 ]] && [[ "$2" -le 365 ]]; then
                    BACKUP_RETENTION_DAYS="$2"
                    shift 2
                else
                    echo "错误: --backup-days 参数必须是 1-365 之间的数字"
                    exit 1
                fi
                ;;
            --no-confirm)
                REQUIRE_CONFIRMATION=false
                shift
                ;;
            --no-protect)
                PROTECT_SYSTEM_FILES=false
                shift
                ;;
            --debug)
                DEBUG=true
                shift
                ;;
            --fast)
                FAST_SEARCH=true
                MAX_SEARCH_DEPTH=2
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            --version|-v)
                echo "应用清理助手 v2.1"
                exit 0
                ;;
            *)
                echo "未知参数: $1"
                echo "使用 --help 查看帮助信息"
                exit 1
                ;;
        esac
    done
}

# 显示帮助信息
show_help() {
    echo "应用清理助手 v2.1 - 智能 macOS 应用程序清理工具"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --depth N          设置最大搜索深度 (1-10, 默认: 3)"
    echo "  --no-backup        禁用自动备份功能"
    echo "  --backup-days N    设置备份保留天数 (1-365, 默认: 180)"
    echo "  --no-confirm       禁用逐个确认删除"
    echo "  --no-protect       禁用系统文件保护"
    echo "  --fast             快速搜索模式 (深度2)"
    echo "  --debug            启用调试模式"
    echo "  --help, -h         显示此帮助信息"
    echo "  --version, -v      显示版本信息"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认配置"
    echo "  $0 --fast                    # 快速搜索模式"
    echo "  $0 --depth 5                 # 深度搜索"
    echo "  $0 --no-backup --no-confirm  # 快速模式（高风险）"
    echo "  $0 --backup-days 30 --debug  # 30天备份，调试模式"
    echo ""
    echo "性能提示:"
    echo "  • 如果搜索卡顿，尝试使用 --fast 参数"
    echo "  • 减少 --depth 参数可以提高性能"
    echo "  • 使用 --debug 查看详细搜索过程"
    echo ""
}

# 日志函数
log_info() {
    local msg="$1"
    echo -e "${BLUE}[INFO]${NC} $msg"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [INFO] $msg" >> "$LOG_FILE"
}

log_success() {
    local msg="$1"
    echo -e "${GREEN}[SUCCESS]${NC} $msg"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [SUCCESS] $msg" >> "$LOG_FILE"
}

log_warning() {
    local msg="$1"
    echo -e "${YELLOW}[WARNING]${NC} $msg"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [WARNING] $msg" >> "$LOG_FILE"
}

log_error() {
    local msg="$1"
    echo -e "${RED}[ERROR]${NC} $msg"
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [ERROR] $msg" >> "$LOG_FILE"
}

log_debug() {
    local msg="$1"
    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo -e "${PURPLE}[DEBUG]${NC} $msg"
    fi
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] [DEBUG] $msg" >> "$LOG_FILE"
}

# 输入验证和安全检查函数
validate_input() {
    local input="$1"
    local input_type="$2"

    case "$input_type" in
        "app_name")
            # 验证应用名称：只允许字母、数字、空格、连字符、下划线
            if [[ ! "$input" =~ ^[a-zA-Z0-9\ \-_\.]+$ ]]; then
                log_error "应用名称包含非法字符: $input"
                return 1
            fi
            # 长度限制
            if [[ ${#input} -gt 100 ]]; then
                log_error "应用名称过长: $input"
                return 1
            fi
            ;;
        "file_path")
            # 验证文件路径：检查是否在安全路径内
            if ! is_safe_path "$input"; then
                log_error "路径不在安全范围内: $input"
                return 1
            fi
            ;;
    esac
    return 0
}

# 检查路径是否安全
is_safe_path() {
    local path="$1"
    local is_safe=false

    # 检查是否在安全路径列表中
    for safe_path in "${SAFE_DELETE_PATHS[@]}"; do
        if [[ "$path" == "$safe_path"* ]]; then
            is_safe=true
            break
        fi
    done

    # 如果不在安全路径中，直接返回false
    if [[ "$is_safe" == "false" ]]; then
        return 1
    fi

    # 检查是否在危险路径黑名单中
    for dangerous_path in "${DANGEROUS_PATHS[@]}"; do
        if [[ "$path" == "$dangerous_path"* ]]; then
            log_warning "路径在危险黑名单中: $path"
            return 1
        fi
    done

    return 0
}

# 转义特殊字符以防止命令注入
escape_for_find() {
    local pattern="$1"
    # 转义find命令中的特殊字符
    echo "$pattern" | sed 's/[[\*\?]/\\&/g'
}

# 初始化函数
initialize() {
    # 创建日志文件
    touch "$LOG_FILE"

    # 设置备份目录
    BACKUP_DIR="$HOME/.app_cleaner_backup/$(date +%Y%m%d_%H%M%S)"
    BACKUP_MANIFEST="$BACKUP_DIR/backup_manifest.txt"

    # 清理旧备份
    cleanup_old_backups

    # 初始化备份清单
    mkdir -p "$BACKUP_DIR"
    echo "# 备份清单 - 创建时间: $(date)" > "$BACKUP_MANIFEST"
    echo "# 格式: 原始路径|备份路径|备份时间" >> "$BACKUP_MANIFEST"

    log_info "应用清理助手已启动"
    log_debug "备份目录: $BACKUP_DIR"
    log_debug "备份清单: $BACKUP_MANIFEST"
    log_debug "日志文件: $LOG_FILE"
    log_debug "配置 - 最大搜索深度: $MAX_SEARCH_DEPTH"
    log_debug "配置 - 启用备份: $ENABLE_BACKUP"
    log_debug "配置 - 备份保留天数: $BACKUP_RETENTION_DAYS"
}

# 清理旧备份
cleanup_old_backups() {
    local backup_base="$HOME/.app_cleaner_backup"
    if [[ -d "$backup_base" ]]; then
        find "$backup_base" -type d -name "20*" -mtime +$BACKUP_RETENTION_DAYS -exec rm -rf {} + 2>/dev/null || true
        log_debug "已清理 $BACKUP_RETENTION_DAYS 天前的旧备份"
    fi
}

# 检查系统关键文件
is_critical_system_file() {
    local file="$1"
    local critical_patterns=(
        "/System/"
        "/usr/bin/"
        "/usr/sbin/"
        "/bin/"
        "/sbin/"
        "/Library/Apple/"
        "/Library/System/"
        "/Applications/Utilities/"
        "/Applications/System Preferences.app"
        "/Applications/Finder.app"
        "/Applications/Safari.app"
    )

    for pattern in "${critical_patterns[@]}"; do
        if [[ "$file" == "$pattern"* ]]; then
            return 0
        fi
    done
    return 1
}

# 检查应用是否正在运行
is_app_running() {
    local app_path="$1"
    local app_name=""

    # 从路径提取应用名称
    if [[ "$app_path" =~ \.app$ ]]; then
        app_name=$(basename "$app_path" .app)
    else
        return 1  # 不是应用程序
    fi

    # 检查进程是否存在
    if pgrep -f "$app_name" > /dev/null 2>&1; then
        return 0  # 应用正在运行
    fi

    # 检查 Activity Monitor 中的进程
    if ps aux | grep -v grep | grep -q "$app_name"; then
        return 0  # 应用正在运行
    fi

    return 1  # 应用未运行
}

# 强制关闭应用
force_quit_app() {
    local app_path="$1"
    local app_name=$(basename "$app_path" .app)

    log_info "尝试关闭应用: $app_name"

    # 首先尝试优雅关闭
    osascript -e "tell application \"$app_name\" to quit" 2>/dev/null

    # 等待3秒
    sleep 3

    # 检查是否还在运行
    if is_app_running "$app_path"; then
        log_warning "优雅关闭失败，强制终止进程"

        # 强制终止进程
        pkill -f "$app_name" 2>/dev/null
        killall "$app_name" 2>/dev/null

        # 再等待2秒
        sleep 2

        # 最后检查
        if is_app_running "$app_path"; then
            log_error "无法关闭应用: $app_name"
            return 1
        else
            log_success "已强制关闭应用: $app_name"
            return 0
        fi
    else
        log_success "已优雅关闭应用: $app_name"
        return 0
    fi
}

# 检查并处理运行中的应用
check_and_handle_running_apps() {
    local files_to_delete=("$@")
    local running_apps=()
    local apps_to_close=()

    # 检查哪些应用正在运行
    for file in "${files_to_delete[@]}"; do
        if [[ "$file" =~ \.app$ ]] && is_app_running "$file"; then
            running_apps+=("$file")
        fi
    done

    # 如果没有运行中的应用，直接返回
    if [[ ${#running_apps[@]} -eq 0 ]]; then
        return 0
    fi

    # 显示运行中的应用
    echo ""
    echo -e "${YELLOW}⚠️  检测到以下应用正在运行：${NC}"
    echo ""

    for app in "${running_apps[@]}"; do
        local app_name=$(basename "$app" .app)
        echo -e "${RED}🔴 $app_name${NC} - $app"
    done

    echo ""
    echo -e "${YELLOW}删除运行中的应用可能导致数据丢失！${NC}"
    echo ""
    echo -e "${GREEN}处理选项：${NC}"
    echo -e "${GREEN}1.${NC} 自动关闭所有运行中的应用"
    echo -e "${GREEN}2.${NC} 逐个选择要关闭的应用"
    echo -e "${GREEN}3.${NC} 跳过运行中的应用，只删除其他文件"
    echo -e "${GREEN}4.${NC} 取消删除操作"
    echo ""

    read -p "请选择处理方式 (1-4): " choice

    case "$choice" in
        1)
            # 自动关闭所有应用
            echo ""
            echo -e "${BLUE}正在关闭所有运行中的应用...${NC}"
            for app in "${running_apps[@]}"; do
                if force_quit_app "$app"; then
                    apps_to_close+=("$app")
                fi
            done
            ;;
        2)
            # 逐个选择
            echo ""
            for app in "${running_apps[@]}"; do
                local app_name=$(basename "$app" .app)
                echo -e "${YELLOW}是否关闭 $app_name？${NC}"
                read -p "关闭此应用 (y/N): " -n 1 -r
                echo ""
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    if force_quit_app "$app"; then
                        apps_to_close+=("$app")
                    fi
                fi
            done
            ;;
        3)
            # 跳过运行中的应用
            echo ""
            echo -e "${BLUE}将跳过运行中的应用，只删除其他文件${NC}"
            # 从删除列表中移除运行中的应用
            local temp_array=()
            for file in "${files_to_delete[@]}"; do
                local skip=false
                for running_app in "${running_apps[@]}"; do
                    if [[ "$file" == "$running_app" ]]; then
                        skip=true
                        break
                    fi
                done
                if [[ "$skip" == "false" ]]; then
                    temp_array+=("$file")
                fi
            done
            # 更新全局数组（这里需要在调用函数中处理）
            echo "${temp_array[@]}"
            return 2  # 特殊返回码表示跳过
            ;;
        4)
            # 取消操作
            echo ""
            log_info "用户取消了删除操作"
            return 1
            ;;
        *)
            echo ""
            log_error "无效选择，取消删除操作"
            return 1
            ;;
    esac

    echo ""
    log_info "运行中应用处理完成"
    return 0
}

# 显示欢迎界面
show_welcome() {
    clear
    echo -e "${NC}"
    echo -e "${GREEN}欢迎使用增强版智能应用清理助手！${NC}"
    echo ""
    echo -e "${YELLOW}💡 推荐使用流程：${NC}"
    echo -e "${BLUE}第一步：${NC} 🔍 智能搜索 "
    echo -e "${BLUE}第二步：${NC} 🎭 安全预演(dry run) "
    echo -e "${BLUE}第三步：${NC} 🗑️  执行清理 "
    echo ""
    echo -e "${YELLOW}📁 备份目录: ${NC}$BACKUP_DIR"
    echo -e "${YELLOW}📝 日志文件: ${NC}$LOG_FILE"
    echo ""
    read -p "按回车键开始..."
    clear
}

# 在 Applications 目录中精确查找匹配的应用
find_matching_apps() {
    local search_term="$1"
    local matching_apps=()

    log_debug "在 Applications 目录中搜索: $search_term"

    # 直接使用更精确的搜索，避免生成过多模式
    local search_patterns_local=(
        "$search_term.app"                                    # 精确匹配
        "$search_term *.app"                                  # 以搜索词开头
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]').app"     # 小写版本
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]') *.app"   # 小写开头
        # 首字母大写版本
        "$(echo "${search_term:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${search_term:1}" | tr '[:upper:]' '[:lower:]').app"
        "$(echo "${search_term:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${search_term:1}" | tr '[:upper:]' '[:lower:]') *.app"
    )

    # 在 Applications 目录中搜索
    for pattern in "${search_patterns_local[@]}"; do
        while IFS= read -r -d '' app; do
            if [[ "$app" =~ \.app$ ]]; then
                # 额外验证：确保应用名确实包含搜索词
                local app_name=$(basename "$app" .app)
                local search_lower=$(echo "$search_term" | tr '[:upper:]' '[:lower:]')
                local app_lower=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')

                # 检查应用名是否以搜索词开头或包含搜索词作为完整单词
                if [[ "$app_lower" == "$search_lower"* ]] || [[ "$app_lower" == *" $search_lower"* ]] || [[ "$app_lower" == *"$search_lower "* ]] || [[ "$app_lower" == "$search_lower" ]]; then
                    matching_apps+=("$app")
                fi
            fi
        done < <(find "/Applications" -maxdepth 2 -iname "$pattern" -type d -print0 2>/dev/null || true)
    done

    # 去重
    if [[ ${#matching_apps[@]} -gt 0 ]]; then
        local temp_file=$(mktemp)
        printf '%s\n' "${matching_apps[@]}" | sort -u > "$temp_file"

        matching_apps=()
        while IFS= read -r line; do
            matching_apps+=("$line")
        done < "$temp_file"

        rm -f "$temp_file"
    fi

    # 使用换行符分隔，避免空格问题
    printf '%s\n' "${matching_apps[@]}"
}

# 显示匹配的应用供用户选择
show_matching_apps() {
    local search_term="$1"
    local apps=("$@")
    # 移除第一个参数（search_term）
    apps=("${apps[@]:1}")

    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📱 找到匹配的应用程序"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${GREEN}搜索 \"$search_term\" 找到以下应用：${NC}"
    echo ""

    for i in "${!apps[@]}"; do
        local app_path="${apps[i]}"
        local app_name=$(basename "$app_path" .app)

        # 确保路径是完整的
        if [[ ! "$app_path" =~ ^/ ]]; then
            app_path="/Applications/$app_path"
        fi

        # 计算应用大小
        local app_size="未知"
        if [[ -e "$app_path" ]]; then
            app_size=$(calculate_file_size "$app_path")
        fi

        echo -e "${GREEN}$((i+1)).${NC} $app_name ${YELLOW}($app_size)${NC}"
        echo -e "   ${BLUE}路径: $app_path${NC}"

        # 检查应用是否正在运行
        if is_app_running "$app_path"; then
            echo -e "   ${RED}🔴 正在运行${NC}"
        else
            echo -e "   ${GREEN}⚪ 未运行${NC}"
        fi
        echo ""
    done

    echo -e "${GREEN}$((${#apps[@]}+1)).${NC} 全部选择"
    echo -e "${GREEN}$((${#apps[@]}+2)).${NC} 自定义搜索（忽略以上结果）"
    echo -e "${GREEN}0.${NC} 重新输入应用名称"
    echo ""
}

# 获取应用程序名称
get_app_name() {
    echo -e "${CYAN}=============================================="
    echo "    📱 输入目标应用程序"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}请输入要清理的应用程序名称：${NC}"
    echo ""
    echo -e "${CYAN}💡 输入提示：${NC}"
    echo "  • 可以输入完整名称：ClashX Pro"
    echo "  • 也可以输入关键词：Clash"
    echo "  • 支持中英文应用名称"
    echo "  • 输入 'list' 查看已安装的应用"
    echo ""

    while true; do
        read -p "应用程序名称 > " input_name

        if [[ -z "$input_name" ]]; then
            echo -e "${RED}❌ 应用程序名称不能为空，请重新输入${NC}"
            continue
        fi

        if [[ "$input_name" == "list" ]]; then
            show_installed_apps
            continue
        fi

        # 输入验证
        if ! validate_input "$input_name" "app_name"; then
            echo -e "${RED}❌ 应用程序名称包含非法字符或格式不正确，请重新输入${NC}"
            continue
        fi

        APP_NAME="$input_name"
        log_info "用户输入应用名称: $APP_NAME"

        # 在 Applications 目录中查找匹配的应用
        echo ""
        echo -e "${BLUE}🔍 正在 Applications 目录中查找匹配的应用...${NC}"
        local matching_apps_str
        matching_apps_str=$(find_matching_apps "$APP_NAME")

        if [[ -n "$matching_apps_str" ]]; then
            # 将换行分隔的字符串转换为数组
            local matching_apps=()
            while IFS= read -r line; do
                if [[ -n "$line" ]]; then
                    matching_apps+=("$line")
                fi
            done <<< "$matching_apps_str"

            # 显示匹配的应用
            show_matching_apps "$APP_NAME" "${matching_apps[@]}"

            # 让用户选择
            read -p "请选择要清理的应用 (0-$((${#matching_apps[@]}+2))) > " app_choice

            case "$app_choice" in
                0)
                    continue  # 重新输入应用名称
                    ;;
                $((${#matching_apps[@]}+1)))
                    # 全部选择
                    SELECTED_APPS=("${matching_apps[@]}")
                    echo ""
                    echo -e "${GREEN}✅ 已选择全部 ${#SELECTED_APPS[@]} 个应用进行清理${NC}"
                    break
                    ;;
                $((${#matching_apps[@]}+2)))
                    # 自定义搜索
                    echo ""
                    echo -e "${YELLOW}📝 将使用自定义搜索模式${NC}"
                    SELECTED_APPS=()
                    break
                    ;;
                *)
                    # 选择特定应用
                    if [[ "$app_choice" =~ ^[0-9]+$ ]] && [[ "$app_choice" -ge 1 ]] && [[ "$app_choice" -le "${#matching_apps[@]}" ]]; then
                        local selected_app="${matching_apps[$((app_choice-1))]}"
                        SELECTED_APPS=("$selected_app")
                        local app_name=$(basename "$selected_app" .app)
                        echo ""
                        echo -e "${GREEN}✅ 已选择应用: $app_name${NC}"
                        break
                    else
                        echo -e "${RED}❌ 无效选择，请重新选择${NC}"
                        continue
                    fi
                    ;;
            esac
        else
            # 没有找到匹配的应用
            echo -e "${YELLOW}⚠️  在 Applications 目录中未找到匹配 \"$APP_NAME\" 的应用${NC}"
            echo ""
            echo -e "${CYAN}💡 处理选项：${NC}"
            echo -e "${GREEN}1.${NC} 进行详细文件搜索（搜索所有相关文件）"
            echo -e "${GREEN}2.${NC} 重新输入应用名称"
            echo -e "${GREEN}3.${NC} 退出程序"
            echo ""

            read -p "请选择处理方式 (1-3): " no_match_choice

            case "$no_match_choice" in
                1)
                    echo ""
                    echo -e "${BLUE}📝 将进行详细文件搜索模式${NC}"
                    SELECTED_APPS=()
                    break
                    ;;
                2)
                    continue  # 重新输入应用名称
                    ;;
                3)
                    echo ""
                    echo -e "${GREEN}👋 感谢使用！${NC}"
                    exit 0
                    ;;
                *)
                    echo -e "${RED}❌ 无效选择，请重新选择${NC}"
                    continue
                    ;;
            esac
        fi
    done

    echo ""
    echo -e "${GREEN}✅ 目标应用程序：${NC} \"$APP_NAME\""
    log_info "用户选择清理应用: $APP_NAME"
}

# 显示已安装的应用程序
show_installed_apps() {
    echo ""
    echo -e "${BLUE}📱 已安装的应用程序（前20个）：${NC}"
    echo ""

    local count=0
    while IFS= read -r app && [[ $count -lt 20 ]]; do
        local app_name=$(basename "$app" .app)
        echo "  • $app_name"
        ((count++))
    done < <(find /Applications -maxdepth 1 -name "*.app" -type d | sort)

    if [[ $count -eq 20 ]]; then
        echo "  ... (还有更多应用)"
    fi
    echo ""
}

# 生成精确搜索模式
generate_search_patterns() {
    local app_name="$1"
    search_patterns=()

    log_debug "为应用 '$app_name' 生成搜索模式"

    # 更精确的搜索模式 - 以应用名开头或包含应用名的完整单词
    search_patterns+=("$app_name.app")           # 精确匹配
    search_patterns+=("$app_name *")             # 应用名开头，后面有空格
    search_patterns+=("*$app_name.app")          # 以应用名结尾
    search_patterns+=("*$app_name *")            # 包含应用名作为完整单词

    # 生成小写版本
    local lowercase=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
    if [[ "$lowercase" != "$app_name" ]]; then
        search_patterns+=("$lowercase.app")
        search_patterns+=("$lowercase *")
        search_patterns+=("*$lowercase.app")
        search_patterns+=("*$lowercase *")
    fi

    # 生成首字母大写版本
    local capitalized=$(echo "${app_name:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${app_name:1}" | tr '[:upper:]' '[:lower:]')
    if [[ "$capitalized" != "$app_name" ]]; then
        search_patterns+=("$capitalized.app")
        search_patterns+=("$capitalized *")
        search_patterns+=("*$capitalized.app")
        search_patterns+=("*$capitalized *")
    fi

    # 去重
    if [[ ${#search_patterns[@]} -gt 0 ]]; then
        local temp_file=$(mktemp)
        printf '%s\n' "${search_patterns[@]}" | sort -u > "$temp_file"

        search_patterns=()
        while IFS= read -r line; do
            search_patterns+=("$line")
        done < "$temp_file"

        rm -f "$temp_file"
    fi

    log_debug "生成了 ${#search_patterns[@]} 个搜索模式"
}

# 检查文件是否应该包含在结果中
should_include_file() {
    local file="$1"

    # 检查是否为系统关键文件
    if is_critical_system_file "$file"; then
        log_debug "跳过系统关键文件: $file"
        return 1
    fi

    # 检查排除模式
    for exclude_pattern in "${exclude_patterns[@]}"; do
        if should_protect_file "$file" "$exclude_pattern"; then
            log_debug "文件被保护模式排除: $file (模式: $exclude_pattern)"
            return 1
        fi
    done

    return 0  # 包含此文件
}

# 改进的保护模式检查
should_protect_file() {
    local file="$1"
    local pattern="$2"
    local filename=$(basename "$file")

    # 精确匹配文件名
    if [[ "$filename" == "$pattern" ]]; then
        return 0
    fi

    # 精确匹配完整路径
    if [[ "$file" == "$pattern" ]]; then
        return 0
    fi

    # 模糊匹配（包含关系）
    if [[ "$file" == *"$pattern"* ]]; then
        return 0
    fi

    # 正则表达式匹配
    if [[ "$file" =~ $pattern ]]; then
        return 0
    fi

    return 1
}

# 显示搜索进度
show_search_progress() {
    local current="$1"
    local total="$2"
    local dir="$3"
    local percentage=$((current * 100 / total))

    # 只在调试模式或显示进度开启时显示
    if [[ "${DEBUG:-false}" == "true" ]] || [[ "${SHOW_PROGRESS:-true}" == "true" ]]; then
        printf "\r${BLUE}🔍 搜索进度: %d/%d (%d%%) - %s${NC}" "$current" "$total" "$percentage" "$(basename "$dir")"
    fi
}

# 检查目录访问权限
check_directory_permission() {
    local dir="$1"

    # 检查目录是否存在
    if [[ ! -e "$dir" ]]; then
        return 1
    fi

    # 检查是否有读取权限
    if [[ ! -r "$dir" ]]; then
        return 2
    fi

    return 0
}

# 提示权限问题
prompt_permission_issue() {
    local dir="$1"
    local issue_type="$2"

    case "$issue_type" in
        1)
            log_debug "目录不存在: $dir"
            ;;
        2)
            echo ""
            echo -e "${YELLOW}⚠️  权限问题：${NC}"
            echo -e "${RED}无法访问目录: $dir${NC}"
            echo -e "${BLUE}这可能需要管理员权限。${NC}"
            echo ""
            read -p "是否要使用 sudo 权限继续搜索此目录？(y/N) > " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                return 0  # 用户同意使用sudo
            else
                log_info "跳过受保护目录: $dir"
                return 1  # 用户拒绝使用sudo
            fi
            ;;
    esac
    return 1
}

# 串行搜索单个目录
search_directory() {
    local dir="$1"
    local pattern="$2"
    local max_depth="$3"

    if [[ ! -d "$dir" ]]; then
        return
    fi

    # 检查目录权限
    check_directory_permission "$dir"
    local perm_result=$?

    if [[ $perm_result -ne 0 ]]; then
        log_debug "跳过无权限目录: $dir"
        return
    fi

    # 转义搜索模式以防止命令注入
    local escaped_pattern
    escaped_pattern=$(escape_for_find "$pattern")

    # 使用find命令搜索
    find "$dir" -maxdepth "$max_depth" \( -name "$escaped_pattern" -o -lname "*$escaped_pattern*" \) 2>/dev/null | \
    while IFS= read -r file; do
        # 安全路径检查
        if is_safe_path "$file" && should_include_file "$file"; then
            echo "$file"
        fi
    done
}



# 串行搜索函数
unified_search() {
    local app_name="$1"

    log_debug "开始搜索，应用名称: $app_name"

    # 定义搜索目录
    local search_dirs=(
        "/Applications"
        "$HOME/Library/Application Support"
        "$HOME/Library/Preferences"
        "$HOME/Library/Caches"
        "$HOME/Library/Logs"
        "$HOME/Library/Containers"
        "$HOME/Library/Group Containers"
        "$HOME/Library/Saved Application State"
    )

    local temp_results=$(mktemp)

    # 串行搜索所有模式和目录
    for pattern in "${search_patterns[@]}"; do
        log_debug "搜索模式: $pattern"

        for dir in "${search_dirs[@]}"; do
            # 检查目录是否存在且安全
            if [[ -d "$dir" ]] && is_safe_path "$dir"; then
                # 使用配置的搜索深度
                search_directory "$dir" "$pattern" "$MAX_SEARCH_DEPTH" >> "$temp_results"
            fi
        done
    done

    # 去重和排序结果
    local final_results=""
    if [[ -s "$temp_results" ]]; then
        final_results=$(sort -u "$temp_results")
    fi

    # 清理临时文件
    rm -f "$temp_results"

    echo "$final_results"
}

# 基于选中应用生成精确搜索模式（修复版）
generate_precise_search_patterns() {
    local selected_apps=("$@")
    local precise_patterns=()

    for app_path in "${selected_apps[@]}"; do
        local app_name=$(basename "$app_path" .app)

        # 只添加应用名称相关的模式，不添加完整路径
        precise_patterns+=("*$app_name*")

        # 生成常见的命名变体
        precise_patterns+=("*$(echo "$app_name" | tr ' ' '.')*")
        precise_patterns+=("*$(echo "$app_name" | tr ' ' '-')*")
        precise_patterns+=("*$(echo "$app_name" | tr ' ' '_')*")

        # 生成小写版本
        local lowercase=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
        if [[ "$lowercase" != "$app_name" ]]; then
            precise_patterns+=("*$lowercase*")
            precise_patterns+=("*$(echo "$lowercase" | tr ' ' '.')*")
            precise_patterns+=("*$(echo "$lowercase" | tr ' ' '-')*")
            precise_patterns+=("*$(echo "$lowercase" | tr ' ' '_')*")
        fi

        # 生成无空格版本
        local no_space="${app_name// /}"
        if [[ "$no_space" != "$app_name" ]]; then
            precise_patterns+=("*$no_space*")
            precise_patterns+=("*$(echo "$no_space" | tr '[:upper:]' '[:lower:]')*")
        fi
    done

    # 去重
    local temp_file=$(mktemp)
    printf '%s\n' "${precise_patterns[@]}" | sort -u > "$temp_file"

    search_patterns=()
    while IFS= read -r line; do
        search_patterns+=("$line")
    done < "$temp_file"

    rm -f "$temp_file"

    log_debug "基于 ${#selected_apps[@]} 个选中应用生成了 ${#search_patterns[@]} 个搜索模式"
}

# 简化的搜索文件函数
search_files() {
    echo "正在搜索相关文件..."

    # 清空结果数组
    found_files=()

    if [[ ${#SELECTED_APPS[@]} -gt 0 ]]; then
        echo "基于选中的应用进行搜索："
        for app in "${SELECTED_APPS[@]}"; do
            local app_name=$(basename "$app" .app)
            echo "  - $app_name"
        done

        # 基于选中应用生成精确搜索模式
        generate_precise_search_patterns "${SELECTED_APPS[@]}"

        log_info "开始精确搜索，基于 ${#SELECTED_APPS[@]} 个选中应用"
    else
        echo "搜索 \"$APP_NAME\" 相关文件..."

        # 生成通用搜索模式
        generate_search_patterns "$APP_NAME"

        log_info "开始通用搜索应用: $APP_NAME"
    fi

    if [[ "${DEBUG:-false}" == "true" ]]; then
        echo "搜索模式: ${search_patterns[*]}"
    fi

    # 执行串行搜索
    local search_results
    search_results=$(unified_search "$APP_NAME")

    # 将结果转换为数组
    if [[ -n "$search_results" ]]; then
        while IFS= read -r line; do
            found_files+=("$line")
        done <<< "$search_results"
    fi

    echo "搜索完成，找到 ${#found_files[@]} 个文件/目录"
}

# 计算文件大小（重新设计，避免颜色代码问题）
calculate_file_size() {
    local file="$1"

    # 检查文件是否存在
    if [[ ! -e "$file" ]]; then
        echo "0B"
        return
    fi

    if [[ -d "$file" ]]; then
        # 对于目录，使用 stat 和 find 命令
        local size_bytes=$(find "$file" -type f -exec stat -f%z {} \; 2>/dev/null | awk '{sum+=$1} END {print sum+0}')

        if [[ "$size_bytes" -gt 0 ]]; then
            # 转换为人类可读格式
            if [[ "$size_bytes" -lt 1024 ]]; then
                echo "${size_bytes}B"
            elif [[ "$size_bytes" -lt 1048576 ]]; then
                echo "$((size_bytes / 1024))KB"
            elif [[ "$size_bytes" -lt 1073741824 ]]; then
                echo "$((size_bytes / 1048576))MB"
            else
                echo "$((size_bytes / 1073741824))GB"
            fi
        else
            # 如果计算失败，统计文件数量
            local file_count=$(find "$file" -type f 2>/dev/null | wc -l | tr -d ' ')
            if [[ "$file_count" -gt 0 ]]; then
                echo "${file_count}个文件"
            else
                echo "空目录"
            fi
        fi
    else
        # 对于文件，使用 stat 命令
        local size_bytes=$(stat -f%z "$file" 2>/dev/null)

        if [[ -n "$size_bytes" && "$size_bytes" =~ ^[0-9]+$ ]]; then
            # 转换为人类可读格式
            if [[ "$size_bytes" -lt 1024 ]]; then
                echo "${size_bytes}B"
            elif [[ "$size_bytes" -lt 1048576 ]]; then
                echo "$((size_bytes / 1024))KB"
            elif [[ "$size_bytes" -lt 1073741824 ]]; then
                echo "$((size_bytes / 1048576))MB"
            else
                echo "$((size_bytes / 1073741824))GB"
            fi
        else
            echo "未知"
        fi
    fi
}

# 显示搜索结果并分析
show_search_results() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📋 搜索结果分析"
    echo "=============================================="
    echo -e "${NC}"

    if [[ ${#found_files[@]} -eq 0 ]]; then
        echo -e "${YELLOW}🤔 未找到 \"$APP_NAME\" 相关文件${NC}"
        echo ""
        echo "可能的原因："
        echo "  • 应用程序未安装"
        echo "  • 应用程序名称不匹配"
        echo "  • 应用程序已被完全清理"
        echo "  • 搜索模式不匹配（尝试使用更通用的关键词）"
        echo ""
        return 1
    fi

    echo -e "${GREEN}🎯 找到 ${#found_files[@]} 个相关文件/目录：${NC}"
    echo ""

    # 分类显示
    local app_files=()
    local user_data_files=()
    local system_files=()
    local total_size=0

    for file in "${found_files[@]}"; do
        if [[ "$file" =~ \.app$ ]] || [[ "$file" =~ /Applications/ ]]; then
            # 应用程序：.app 文件或在 Applications 目录中
            app_files+=("$file")
        elif [[ "$file" =~ ^/Library/ ]] || [[ "$file" =~ ^/System/ ]] || [[ "$file" =~ ^/usr/ ]]; then
            # 系统文件：在系统目录中
            system_files+=("$file")
        elif [[ "$file" =~ ^$HOME/Library/ ]]; then
            # 用户数据：在用户Library目录中
            user_data_files+=("$file")
        else
            # 其他用户文件
            user_data_files+=("$file")
        fi
    done

    # 显示应用程序
    if [[ ${#app_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📱 应用程序 (${#app_files[@]} 个)：${NC}"
        for file in "${app_files[@]}"; do
            local size=$(calculate_file_size "$file")
            echo "  • $file ${YELLOW}($size)${NC}"
        done
        echo ""
    fi

    # 显示用户数据文件
    if [[ ${#user_data_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📁 用户数据 (${#user_data_files[@]} 个)：${NC}"
        for file in "${user_data_files[@]}"; do
            local size=$(calculate_file_size "$file")
            echo "  • $file ${YELLOW}($size)${NC}"
        done
        echo ""
    fi

    # 显示系统文件
    if [[ ${#system_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}⚙️  系统文件 (${#system_files[@]} 个)：${NC}"
        for file in "${system_files[@]}"; do
            local size=$(calculate_file_size "$file")
            echo "  • $file ${YELLOW}($size)${NC}"
        done
        echo ""
    fi

    # 计算总大小
    echo -e "${CYAN}📊 统计信息：${NC}"
    echo "  • 应用程序: ${#app_files[@]} 个"
    echo "  • 用户数据: ${#user_data_files[@]} 个"
    echo "  • 系统文件: ${#system_files[@]} 个"
    echo "  • 总计: ${#found_files[@]} 个文件/目录"
    echo ""

    # 智能分析是否需要保护模式
    analyze_protection_need

    log_info "搜索结果分析完成"
    return 0
}

# 智能分析是否需要保护模式
analyze_protection_need() {
    local need_protection=false
    local protection_suggestions=()
    local risk_level="low"

    # 检查是否有多个相似应用
    local app_count=0
    local different_apps=()
    local system_file_count=0

    for file in "${found_files[@]}"; do
        if [[ "$file" =~ \.app$ ]]; then
            ((app_count++))
            local app_name=$(basename "$file" .app)
            different_apps+=("$app_name")
        elif [[ "$file" =~ ^/Library/ ]]; then
            ((system_file_count++))
        fi
    done

    # 评估风险级别
    if [[ $system_file_count -gt 0 ]]; then
        risk_level="high"
    elif [[ $app_count -gt 1 ]]; then
        risk_level="medium"
    fi

    # 显示风险评估
    echo -e "${CYAN}🛡️  安全评估：${NC}"
    case "$risk_level" in
        "high")
            echo -e "${RED}  • 风险级别: 高 (包含系统文件)${NC}"
            echo -e "${YELLOW}  • 建议: 强烈推荐使用预演模式${NC}"
            ;;
        "medium")
            echo -e "${YELLOW}  • 风险级别: 中 (多个相关应用)${NC}"
            echo -e "${YELLOW}  • 建议: 建议使用保护模式${NC}"
            ;;
        "low")
            echo -e "${GREEN}  • 风险级别: 低 (单一目标应用)${NC}"
            echo -e "${GREEN}  • 建议: 可以安全清理${NC}"
            ;;
    esac
    echo ""

    if [[ $app_count -gt 1 ]]; then
        need_protection=true
        echo -e "${YELLOW}⚠️  检测到多个相关应用程序：${NC}"
        for app in "${different_apps[@]}"; do
            echo "  • $app"
        done
        echo ""
        echo -e "${CYAN}💡 建议：如果您只想删除其中某个应用，请使用保护模式${NC}"

        # 生成保护建议
        for app in "${different_apps[@]}"; do
            if [[ "$app" != "$APP_NAME" ]]; then
                protection_suggestions+=("$app")
            fi
        done
    fi

    if [[ $system_file_count -gt 0 ]]; then
        echo -e "${RED}⚠️  检测到 $system_file_count 个系统文件${NC}"
        echo -e "${YELLOW}💡 系统文件删除需要管理员权限，请谨慎操作${NC}"
        echo ""
    fi

    # 如果需要保护模式，提供选项
    if [[ "$need_protection" == "true" ]]; then
        offer_protection_mode "${protection_suggestions[@]}"
    fi

    log_info "安全评估完成，风险级别: $risk_level"
}

# 提供保护模式选项
offer_protection_mode() {
    local suggestions=("$@")

    echo -e "${YELLOW}🛡️  是否需要设置保护模式？${NC}"
    echo ""
    echo -e "${GREEN}1.${NC} 是 - 保护某些应用不被删除"
    echo -e "${GREEN}2.${NC} 否 - 删除所有找到的文件"
    echo -e "${GREEN}3.${NC} 智能保护 - 自动保护重要应用"
    echo ""

    read -p "请选择 (1/2/3) > " protection_choice

    case "$protection_choice" in
        "1")
            setup_manual_protection "${suggestions[@]}"
            ;;
        "3")
            setup_smart_protection
            ;;
        *)
            log_info "用户选择不使用保护模式"
            ;;
    esac
    echo ""
}

# 手动设置保护模式
setup_manual_protection() {
    local suggestions=("$@")

    echo ""
    echo -e "${CYAN}建议保护以下应用：${NC}"
    for i in "${!suggestions[@]}"; do
        echo -e "${GREEN}$((i+1)).${NC} ${suggestions[i]}"
    done
    echo -e "${GREEN}$((${#suggestions[@]}+1)).${NC} 自定义输入"
    echo -e "${GREEN}0.${NC} 全部保护"
    echo ""

    read -p "请选择要保护的应用 (多选用空格分隔，如: 1 2) > " protect_choices

    exclude_patterns=()
    for choice in $protect_choices; do
        if [[ "$choice" == "0" ]]; then
            exclude_patterns=("${suggestions[@]}")
            break
        elif [[ "$choice" =~ ^[0-9]+$ ]] && [[ "$choice" -le "${#suggestions[@]}" ]] && [[ "$choice" -gt 0 ]]; then
            exclude_patterns+=("${suggestions[$((choice-1))]}")
        elif [[ "$choice" == "$((${#suggestions[@]}+1))" ]]; then
            echo "请输入自定义保护关键词（多个用空格分隔）："
            read -p "> " custom_protection
            if [[ -n "$custom_protection" ]]; then
                for keyword in $custom_protection; do
                    exclude_patterns+=("$keyword")
                done
            fi
        fi
    done

    if [[ ${#exclude_patterns[@]} -gt 0 ]]; then
        echo ""
        echo -e "${GREEN}✅ 保护模式已设置，以下内容将被保护：${NC}"
        for pattern in "${exclude_patterns[@]}"; do
            echo "  🛡️  $pattern"
        done
        log_info "手动保护模式已设置，保护 ${#exclude_patterns[@]} 个模式"
    fi
}

# 智能保护模式
setup_smart_protection() {
    echo ""
    echo -e "${CYAN}🤖 启用智能保护模式...${NC}"

    # 智能保护规则
    local smart_patterns=(
        "Safari"
        "Chrome"
        "Firefox"
        "System Preferences"
        "Finder"
        "Terminal"
        "Activity Monitor"
        "Disk Utility"
        "Keychain Access"
        "Console"
    )

    exclude_patterns=()
    local protected_count=0

    for file in "${found_files[@]}"; do
        local filename=$(basename "$file" .app)
        for pattern in "${smart_patterns[@]}"; do
            if [[ "$filename" == *"$pattern"* ]]; then
                exclude_patterns+=("$pattern")
                ((protected_count++))
                break
            fi
        done
    done

    if [[ $protected_count -gt 0 ]]; then
        echo -e "${GREEN}✅ 智能保护已启用，自动保护了 $protected_count 个重要应用${NC}"
        log_info "智能保护模式已启用，保护了 $protected_count 个应用"
    else
        echo -e "${YELLOW}ℹ️  未检测到需要智能保护的应用${NC}"
        log_info "智能保护模式未发现需要保护的应用"
    fi
}

# 显示操作菜单
show_action_menu() {
    echo -e "${CYAN}=============================================="
    echo "    🎯 选择执行操作"
    echo "=============================================="
    echo -e "${NC}"
    echo "基于搜索结果，您可以执行以下操作："
    echo ""
    echo -e "${GREEN}1.${NC} 🎭 ${YELLOW}安全预演${NC} - 显示候选删除文件（强烈推荐）"
    echo -e "${GREEN}2.${NC} 🗑️  ${GREEN}智能清理${NC} - 逐个确认删除，防止误删"
    echo -e "${GREEN}3.${NC} ⚡ ${RED}快速清理${NC} - 自动删除所有候选文件（高风险）"
    echo -e "${GREEN}4.${NC} 🔍 ${BLUE}重新搜索${NC} - 修改搜索条件"
    echo -e "${GREEN}5.${NC} 🛡️  ${PURPLE}保护设置${NC} - 修改保护模式"
    echo -e "${GREEN}6.${NC} 📋 ${CYAN}详细信息${NC} - 查看文件详细信息"
    echo -e "${GREEN}7.${NC} 🔄 ${BLUE}恢复备份${NC} - 从备份恢复文件"
    echo -e "${GREEN}8.${NC} 🚪 ${NC}退出程序"
    echo ""
    echo -e "${YELLOW}💡 安全提示：${NC}"
    echo -e "  • 首次使用请选择 '安全预演' 确认操作"
    echo -e "  • '智能清理' 会逐个确认每个文件，防止误删相似文件"
    echo -e "  • 所有删除操作都会自动创建备份，支持一键恢复"
    echo ""
}

# 增强的备份函数
create_backup_enhanced() {
    local file="$1"
    local backup_name="$(basename "$file")_$(date +%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"

    if [[ ! "$ENABLE_BACKUP" == "true" ]]; then
        log_warning "备份功能已禁用，这是不安全的操作"
        return 0
    fi

    # 检查文件是否存在
    if [[ ! -e "$file" ]]; then
        log_error "要备份的文件不存在: $file"
        return 1
    fi

    # 检查备份目录
    if [[ ! -d "$BACKUP_DIR" ]]; then
        mkdir -p "$BACKUP_DIR" || {
            log_error "无法创建备份目录: $BACKUP_DIR"
            return 1
        }
    fi

    # 检查磁盘空间
    local file_size=$(du -sk "$file" 2>/dev/null | cut -f1)
    local available_space=$(df "$BACKUP_DIR" | tail -1 | awk '{print $4}')

    if [[ $file_size -gt $available_space ]]; then
        log_error "磁盘空间不足，无法创建备份: $file"
        return 1
    fi

    # 创建备份
    log_info "创建备份: $file -> $backup_path"
    if cp -R "$file" "$backup_path" 2>/dev/null; then
        # 记录到备份清单
        echo "$file|$backup_path|$(date +%s)" >> "$BACKUP_MANIFEST"
        log_success "备份创建成功: $(basename "$file")"
        return 0
    else
        log_error "备份创建失败: $file"
        return 1
    fi
}

# 验证备份完整性
verify_backup() {
    local original_file="$1"
    local backup_path="$2"

    if [[ ! -e "$backup_path" ]]; then
        log_error "备份文件不存在: $backup_path"
        return 1
    fi

    # 对于文件，比较大小
    if [[ -f "$original_file" ]] && [[ -f "$backup_path" ]]; then
        local orig_size=$(stat -f%z "$original_file" 2>/dev/null || echo 0)
        local backup_size=$(stat -f%z "$backup_path" 2>/dev/null || echo 0)

        if [[ "$orig_size" != "$backup_size" ]]; then
            log_error "备份文件大小不匹配: $original_file"
            return 1
        fi
    fi

    # 对于目录，比较文件数量
    if [[ -d "$original_file" ]] && [[ -d "$backup_path" ]]; then
        local orig_count=$(find "$original_file" -type f 2>/dev/null | wc -l)
        local backup_count=$(find "$backup_path" -type f 2>/dev/null | wc -l)

        if [[ "$orig_count" != "$backup_count" ]]; then
            log_warning "备份目录文件数量不匹配: $original_file"
            # 对于目录，只警告不返回错误
        fi
    fi

    return 0
}

# 预演删除操作
preview_deletion() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🎭 安全预演模式"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}以下是将要执行的删除操作（仅预览，不会实际删除）：${NC}"
    echo ""

    local will_delete=()
    local will_protect=()
    local total_size=0

    # 根据保护模式过滤文件
    for file in "${found_files[@]}"; do
        local should_protect=false

        for pattern in "${exclude_patterns[@]}"; do
            if should_protect_file "$file" "$pattern"; then
                should_protect=true
                will_protect+=("$file")
                break
            fi
        done

        if [[ "$should_protect" == "false" ]]; then
            will_delete+=("$file")
        fi
    done

    # 显示将要删除的文件
    if [[ ${#will_delete[@]} -gt 0 ]]; then
        echo -e "${RED}🗑️  候选删除文件：${NC}"
        local count=0
        for file in "${will_delete[@]}"; do
            ((count++))
            local size=$(calculate_file_size "$file")
            local file_type=""

            if [[ -d "$file" ]]; then
                file_type="📁"
            elif [[ "$file" =~ \.app$ ]]; then
                file_type="📱"
            else
                file_type="📄"
            fi

            echo -e "${CYAN}[$count] $file_type $file ${YELLOW}($size)${NC}"

            # 显示文件详细信息
            if [[ -e "$file" ]]; then
                local mtime=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || echo "未知")
                echo -e "    ${PURPLE}修改时间: $mtime${NC}"

                # 检查是否为系统关键文件
                if is_critical_system_file "$file"; then
                    echo -e "    ${RED}⚠️  警告：这是系统关键文件！${NC}"
                fi

                # 显示目录内容概览
                if [[ -d "$file" ]]; then
                    local item_count=$(find "$file" -type f 2>/dev/null | wc -l | xargs)
                    echo -e "    ${GREEN}包含文件: $item_count 个${NC}"
                fi
            fi
        done
        echo ""
        echo -e "${GREEN}📊 统计信息：${NC}"
        echo -e "  • 候选删除：${#will_delete[@]} 个文件/目录"
        echo -e "  • 备份将保存到：$BACKUP_DIR"
        echo -e "  • 备份保留时间：$BACKUP_RETENTION_DAYS 天"
        echo ""
        echo -e "${YELLOW}💡 重要提示：${NC}"
        echo -e "  • 在智能清理模式中，您可以逐个确认要删除的文件"
        echo -e "  • 这可以防止误删相似但不同的文件"
        echo -e "  • 建议仔细检查每个文件后再确认删除"
    else
        echo -e "${YELLOW}⚠️  根据保护设置，没有文件将被删除${NC}"
    fi

    # 显示受保护的文件
    if [[ ${#will_protect[@]} -gt 0 ]]; then
        echo ""
        echo -e "${GREEN}🛡️  受保护的文件（将保留）：${NC}"
        for file in "${will_protect[@]}"; do
            local size=$(calculate_file_size "$file")
            echo "  • $file ${YELLOW}($size)${NC}"
        done
    fi

    # 检查运行中的应用（预演模式）
    if [[ ${#will_delete[@]} -gt 0 ]]; then
        echo -e "${BLUE}🔍 检查应用运行状态...${NC}"
        local running_apps=()

        # 检查哪些应用正在运行
        for file in "${will_delete[@]}"; do
            if [[ "$file" =~ \.app$ ]] && is_app_running "$file"; then
                running_apps+=("$file")
            fi
        done

        # 显示运行中的应用
        if [[ ${#running_apps[@]} -gt 0 ]]; then
            echo ""
            echo -e "${YELLOW}⚠️  检测到以下应用正在运行：${NC}"
            echo ""

            for app in "${running_apps[@]}"; do
                local app_name=$(basename "$app" .app)
                echo -e "${RED}🔴 $app_name${NC} - $app"
            done

            echo ""
            echo -e "${YELLOW}💡 预演提示：${NC}"
            echo -e "  • 这些应用在实际删除时需要先关闭"
            echo -e "  • 智能清理模式会提供关闭选项"
            echo -e "  • 建议先手动关闭这些应用"
            echo ""
        else
            echo -e "${GREEN}✅ 没有检测到运行中的应用${NC}"
            echo ""
        fi
    fi

    echo ""
    echo -e "${YELLOW}💡 这是预演模式，没有实际删除任何文件${NC}"
    echo -e "${CYAN}🔒 实际删除时会自动创建备份并逐个确认${NC}"
    echo ""

    log_info "预演完成，候选删除 ${#will_delete[@]} 个文件，保护 ${#will_protect[@]} 个文件，运行中应用 ${#running_apps[@]} 个"
}

# 增强的安全删除函数
safe_delete_file_enhanced() {
    local file="$1"
    local backup_path=""

    # 安全性检查
    if [[ ! -e "$file" ]]; then
        log_warning "文件不存在: $file"
        return 1
    fi

    # 路径安全检查
    if ! is_safe_path "$file"; then
        log_error "拒绝删除不安全路径的文件: $file"
        return 1
    fi

    # 检查是否为系统关键文件
    if is_critical_system_file "$file"; then
        log_error "拒绝删除系统关键文件: $file"
        return 1
    fi

    # 强制备份检查
    if [[ "$ENABLE_BACKUP" == "true" ]]; then
        log_info "正在备份文件: $(basename "$file")"
        if ! create_backup_enhanced "$file"; then
            log_error "备份失败，停止删除操作: $file"
            return 1
        fi

        # 获取备份路径用于验证
        backup_path=$(tail -1 "$BACKUP_MANIFEST" | cut -d'|' -f2)

        # 验证备份完整性
        if ! verify_backup "$file" "$backup_path"; then
            log_error "备份验证失败，停止删除操作: $file"
            return 1
        fi

        log_success "备份验证成功: $(basename "$file")"
    else
        log_warning "备份功能已禁用，这是高风险操作"
        read -p "确定要在没有备份的情况下删除 $file 吗？(输入 'YES' 确认): " confirm
        if [[ "$confirm" != "YES" ]]; then
            log_info "用户取消删除操作"
            return 1
        fi
    fi

    # 记录到删除栈（用于回滚）
    DELETED_FILES_STACK+=("$file|$backup_path")

    # 执行删除
    log_info "删除文件: $file"
    if rm -rf "$file" 2>/dev/null; then
        log_success "删除成功: $(basename "$file")"
        return 0
    else
        log_error "删除失败: $file"
        # 从删除栈中移除
        unset 'DELETED_FILES_STACK[-1]'
        return 1
    fi
}

# 回滚删除操作
rollback_deletions() {
    local rollback_reason="$1"

    log_warning "开始回滚删除操作，原因: $rollback_reason"

    if [[ ${#DELETED_FILES_STACK[@]} -eq 0 ]]; then
        log_info "没有需要回滚的删除操作"
        return 0
    fi

    local rollback_count=0
    local failed_count=0

    # 从栈顶开始回滚（后删除的先恢复）
    for ((i=${#DELETED_FILES_STACK[@]}-1; i>=0; i--)); do
        local entry="${DELETED_FILES_STACK[i]}"
        local original_file="${entry%|*}"
        local backup_path="${entry#*|}"

        if [[ -n "$backup_path" ]] && [[ -e "$backup_path" ]]; then
            log_info "恢复文件: $original_file"

            # 创建目标目录
            local target_dir=$(dirname "$original_file")
            mkdir -p "$target_dir" 2>/dev/null

            # 恢复文件
            if cp -R "$backup_path" "$original_file" 2>/dev/null; then
                log_success "恢复成功: $(basename "$original_file")"
                ((rollback_count++))
            else
                log_error "恢复失败: $original_file"
                ((failed_count++))
            fi
        else
            log_warning "备份文件不存在，无法恢复: $original_file"
            ((failed_count++))
        fi
    done

    # 清空删除栈
    DELETED_FILES_STACK=()

    log_info "回滚操作完成: 成功恢复 $rollback_count 个文件，失败 $failed_count 个"
    return $failed_count
}

# 回滚操作
rollback_operations() {
    local failed_file="$1"
    log_error "删除失败，开始回滚操作..."

    if [[ -f "$BACKUP_DIR/backup_list.txt" ]]; then
        while IFS= read -r backed_file; do
            if [[ "$backed_file" == "$failed_file" ]]; then
                break
            fi

            local backup_path="$BACKUP_DIR/$(basename "$backed_file")"
            if [[ -e "$backup_path" ]]; then
                log_info "恢复: $backed_file"
                cp -R "$backup_path" "$backed_file" 2>/dev/null || {
                    log_error "恢复失败: $backed_file"
                }
            fi
        done < "$BACKUP_DIR/backup_list.txt"
    fi
}

# 逐个确认删除文件
confirm_individual_deletions() {
    local candidate_files=("$@")
    local confirmed_files=()

    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🔍 逐个确认删除文件"
    echo "=============================================="
    echo -e "${NC}"
    echo -e "${YELLOW}请逐个确认要删除的文件。这可以防止误删相似但不同的文件。${NC}"
    echo ""

    local total=${#candidate_files[@]}
    local current=0

    for file in "${candidate_files[@]}"; do
        ((current++))
        echo -e "${BLUE}[$current/$total] 文件确认${NC}"
        echo -e "${CYAN}文件路径: $file${NC}"

        # 显示文件详细信息
        if [[ -e "$file" ]]; then
            local size=$(calculate_file_size "$file")
            local mtime=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || echo "未知")

            if [[ -d "$file" ]]; then
                echo -e "${GREEN}类型: 📁 目录${NC}"
                local item_count=$(find "$file" -type f 2>/dev/null | wc -l | xargs)
                echo -e "${GREEN}包含文件: $item_count 个${NC}"
            elif [[ "$file" =~ \.app$ ]]; then
                echo -e "${GREEN}类型: 📱 应用程序${NC}"
            else
                echo -e "${GREEN}类型: 📄 文件${NC}"
            fi

            echo -e "${GREEN}大小: $size${NC}"
            echo -e "${GREEN}修改时间: $mtime${NC}"

            # 检查是否为系统关键文件
            if is_critical_system_file "$file"; then
                echo -e "${RED}⚠️  警告: 这是系统关键文件！${NC}"
            fi

            # 显示文件内容预览（对于小文件）
            if [[ -f "$file" ]] && [[ $(stat -f%z "$file" 2>/dev/null || echo 0) -lt 1024 ]]; then
                echo -e "${PURPLE}内容预览:${NC}"
                head -3 "$file" 2>/dev/null | sed 's/^/  /' || echo "  (无法预览)"
            fi
        else
            echo -e "${RED}❌ 文件不存在${NC}"
        fi

        echo ""
        echo -e "${YELLOW}选择操作:${NC}"
        echo -e "${GREEN}y${NC} - 删除此文件"
        echo -e "${RED}n${NC} - 保留此文件"
        echo -e "${BLUE}s${NC} - 跳过剩余文件确认"
        echo -e "${PURPLE}q${NC} - 退出删除操作"
        echo ""

        while true; do
            read -p "请选择 (y/n/s/q): " -n 1 -r
            echo ""

            case $REPLY in
                [Yy])
                    confirmed_files+=("$file")
                    echo -e "${GREEN}✅ 已确认删除: $(basename "$file")${NC}"
                    break
                    ;;
                [Nn])
                    echo -e "${YELLOW}⏭️  已跳过: $(basename "$file")${NC}"
                    break
                    ;;
                [Ss])
                    echo -e "${BLUE}⚡ 自动确认剩余所有文件${NC}"
                    confirmed_files+=("$file")
                    # 添加剩余文件
                    for ((i=current; i<total; i++)); do
                        confirmed_files+=("${candidate_files[i]}")
                    done
                    echo -e "${GREEN}✅ 已确认删除剩余 $((total-current+1)) 个文件${NC}"
                    echo "${confirmed_files[@]}"
                    return 0
                    ;;
                [Qq])
                    echo -e "${RED}🚪 用户选择退出删除操作${NC}"
                    echo ""
                    return 1
                    ;;
                *)
                    echo -e "${RED}无效选择，请输入 y/n/s/q${NC}"
                    ;;
            esac
        done
        echo ""
    done

    # 返回确认的文件列表
    echo "${confirmed_files[@]}"
    return 0
}

# 实际删除文件
execute_deletion() {
    local mode="$1"  # interactive 或 auto

    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🗑️  执行安全删除操作"
    echo "=============================================="
    echo -e "${NC}"

    local will_delete=()
    local will_protect=()

    # 根据保护模式过滤文件
    for file in "${found_files[@]}"; do
        local should_protect=false

        for pattern in "${exclude_patterns[@]}"; do
            if should_protect_file "$file" "$pattern"; then
                should_protect=true
                will_protect+=("$file")
                break
            fi
        done

        if [[ "$should_protect" == "false" ]]; then
            will_delete+=("$file")
        fi
    done

    if [[ ${#will_delete[@]} -eq 0 ]]; then
        log_warning "根据保护设置，没有文件需要删除"
        return
    fi

    # 显示删除计划
    echo -e "${YELLOW}📋 删除计划：${NC}"
    echo "  • 候选删除: ${#will_delete[@]} 个文件/目录"
    echo "  • 将保护: ${#will_protect[@]} 个文件/目录"
    echo "  • 备份目录: $BACKUP_DIR"
    echo ""

    # 逐个确认删除（交互模式）
    local final_delete_list=()
    if [[ "$mode" == "interactive" ]] && [[ "$REQUIRE_CONFIRMATION" == "true" ]]; then
        echo -e "${YELLOW}🔍 开始逐个确认删除文件...${NC}"

        local confirmed_files_str
        confirmed_files_str=$(confirm_individual_deletions "${will_delete[@]}")
        local confirm_exit_code=$?

        if [[ $confirm_exit_code -ne 0 ]]; then
            log_info "用户取消了删除操作"
            return
        fi

        # 将确认的文件字符串转换为数组
        if [[ -n "$confirmed_files_str" ]]; then
            read -ra final_delete_list <<< "$confirmed_files_str"
        fi

        if [[ ${#final_delete_list[@]} -eq 0 ]]; then
            log_info "没有文件被确认删除"
            return
        fi

        echo ""
        echo -e "${GREEN}📋 最终删除清单：${NC}"
        echo "  • 确认删除: ${#final_delete_list[@]} 个文件/目录"
        echo "  • 跳过删除: $((${#will_delete[@]} - ${#final_delete_list[@]})) 个文件/目录"
        echo ""
    else
        # 自动模式或禁用确认：使用所有候选文件
        final_delete_list=("${will_delete[@]}")
        if [[ "$mode" == "auto" ]]; then
            echo -e "${RED}⚡ 快速删除模式：将删除所有候选文件${NC}"
        fi
    fi

    # 检查运行中的应用
    echo -e "${BLUE}🔍 检查应用运行状态...${NC}"
    local running_check_result
    running_check_result=$(check_and_handle_running_apps "${final_delete_list[@]}")
    local running_exit_code=$?

    case $running_exit_code in
        1)
            # 用户取消操作
            return
            ;;
        2)
            # 跳过运行中的应用，更新删除列表
            if [[ -n "$running_check_result" ]]; then
                read -ra final_delete_list <<< "$running_check_result"
                echo ""
                echo -e "${YELLOW}📋 更新后的删除清单：${NC}"
                echo "  • 将删除: ${#final_delete_list[@]} 个文件/目录"
                echo ""
            fi
            ;;
        0)
            # 正常处理，继续
            ;;
    esac

    # 最终确认（如果还没有确认过）
    if [[ "$mode" == "interactive" ]] && [[ "$REQUIRE_CONFIRMATION" != "true" ]]; then
        echo -e "${RED}⚠️  即将执行删除操作${NC}"
        echo -e "${YELLOW}所有文件将自动备份，可通过恢复功能找回${NC}"
        echo ""
        read -p "确定要继续吗？(y/N) > " -n 1 -r
        echo ""
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            return
        fi
    fi

    log_info "开始安全删除操作..."

    local deleted_count=0
    local failed_count=0
    local backup_count=0

    # 创建备份目录
    mkdir -p "$BACKUP_DIR"
    echo "# 备份创建时间: $(date)" > "$BACKUP_DIR/backup_info.txt"
    echo "# 应用名称: $APP_NAME" >> "$BACKUP_DIR/backup_info.txt"
    echo "# 删除的文件列表:" >> "$BACKUP_DIR/backup_info.txt"

    # 逐个删除确认的文件（增强版）
    for file in "${final_delete_list[@]}"; do
        if safe_delete_file_enhanced "$file"; then
            ((deleted_count++))
            echo "$file" >> "$BACKUP_DIR/backup_info.txt"
        else
            ((failed_count++))
            log_error "删除失败: $file"

            # 删除失败时的处理策略
            if [[ "$mode" == "interactive" ]]; then
                echo ""
                echo -e "${RED}⚠️  删除失败: $(basename "$file")${NC}"
                echo -e "${YELLOW}选择处理方式：${NC}"
                echo -e "${GREEN}1.${NC} 继续删除其他文件"
                echo -e "${GREEN}2.${NC} 停止删除并回滚已删除的文件"
                echo -e "${GREEN}3.${NC} 停止删除但保持已删除的文件"
                echo ""

                read -p "请选择 (1/2/3): " -n 1 -r
                echo ""

                case $REPLY in
                    1)
                        log_info "用户选择继续删除其他文件"
                        continue
                        ;;
                    2)
                        log_info "用户选择停止删除并回滚"
                        rollback_deletions "用户请求回滚"
                        return 1
                        ;;
                    3)
                        log_info "用户选择停止删除但保持已删除的文件"
                        break
                        ;;
                    *)
                        log_info "无效选择，默认停止删除"
                        break
                        ;;
                esac
            else
                # 自动模式下，删除失败时自动回滚
                log_warning "自动模式下删除失败，执行回滚操作"
                rollback_deletions "删除失败自动回滚"
                return 1
            fi
        fi
    done

    # 报告结果
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📊 删除操作报告"
    echo "=============================================="
    echo -e "${NC}"
    log_success "成功删除: $deleted_count 个文件/目录"
    if [[ $failed_count -gt 0 ]]; then
        log_error "删除失败: $failed_count 个文件/目录"
    fi

    if [[ $deleted_count -gt 0 ]]; then
        log_success "备份已保存到: $BACKUP_DIR"
        echo -e "${CYAN}💡 如需恢复，请使用菜单中的 '恢复备份' 功能${NC}"
    fi

    # 显示受保护的文件
    if [[ ${#will_protect[@]} -gt 0 ]]; then
        echo ""
        log_info "受保护的文件（已保留）："
        for file in "${will_protect[@]}"; do
            echo "  🛡️  $file"
        done
    fi

    echo ""
}

# 显示文件详细信息
show_detailed_info() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    📋 文件详细信息"
    echo "=============================================="
    echo -e "${NC}"

    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_warning "没有找到文件"
        return
    fi

    local total_size=0
    local file_count=0
    local dir_count=0

    for file in "${found_files[@]}"; do
        ((file_count++))
        echo -e "${BLUE}[$file_count] $file${NC}"

        if [[ -e "$file" ]]; then
            # 文件类型
            if [[ -d "$file" ]]; then
                echo "  📁 类型: 目录"
                ((dir_count++))
            elif [[ -f "$file" ]]; then
                echo "  📄 类型: 文件"
            elif [[ -L "$file" ]]; then
                echo "  🔗 类型: 符号链接"
                local target=$(readlink "$file")
                echo "  🎯 链接目标: $target"
            fi

            # 文件大小
            local size=$(calculate_file_size "$file")
            echo "  📏 大小: $size"

            # 修改时间
            local mtime=$(stat -f "%Sm" -t "%Y-%m-%d %H:%M:%S" "$file" 2>/dev/null || echo "未知")
            echo "  🕒 修改时间: $mtime"

            # 权限
            local perms=$(ls -ld "$file" | awk '{print $1}' 2>/dev/null || echo "未知")
            echo "  🔐 权限: $perms"

            # 所有者
            local owner=$(ls -ld "$file" | awk '{print $3}' 2>/dev/null || echo "未知")
            echo "  👤 所有者: $owner"

            # 检查是否为系统关键文件
            if is_critical_system_file "$file"; then
                echo -e "  ${RED}⚠️  系统关键文件${NC}"
            fi

            # 检查是否受保护
            local protected=false
            for pattern in "${exclude_patterns[@]}"; do
                if should_protect_file "$file" "$pattern"; then
                    echo -e "  ${GREEN}🛡️  受保护文件${NC}"
                    protected=true
                    break
                fi
            done

        else
            echo -e "  ${RED}❌ 文件不存在${NC}"
        fi
        echo ""
    done

    echo -e "${CYAN}📊 统计摘要：${NC}"
    echo "  • 总文件数: $file_count"
    echo "  • 目录数: $dir_count"
    echo "  • 文件数: $((file_count - dir_count))"
    echo ""
}

# 恢复备份
restore_backup() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🔄 恢复备份"
    echo "=============================================="
    echo -e "${NC}"

    local backup_base="$HOME/.app_cleaner_backup"

    if [[ ! -d "$backup_base" ]]; then
        log_warning "未找到备份目录"
        return
    fi

    # 列出可用的备份
    echo -e "${YELLOW}📁 可用的备份：${NC}"
    echo ""

    local backup_dirs=()
    local count=0

    while IFS= read -r -d '' backup_dir; do
        ((count++))
        backup_dirs+=("$backup_dir")
        local backup_name=$(basename "$backup_dir")
        local backup_date=$(echo "$backup_name" | sed 's/_/ /g' | sed 's/\([0-9]\{8\}\)/\1 /')

        echo -e "${GREEN}$count.${NC} $backup_date"

        # 显示备份信息
        if [[ -f "$backup_dir/backup_info.txt" ]]; then
            local app_name=$(grep "# 应用名称:" "$backup_dir/backup_info.txt" | cut -d: -f2 | xargs)
            local file_count=$(grep -v "^#" "$backup_dir/backup_info.txt" | wc -l | xargs)
            echo "   📱 应用: $app_name"
            echo "   📄 文件数: $file_count"
        fi
        echo ""
    done < <(find "$backup_base" -type d -name "20*" -print0 | sort -z)

    if [[ $count -eq 0 ]]; then
        log_warning "没有找到可用的备份"
        return
    fi

    echo -e "${GREEN}0.${NC} 返回主菜单"
    echo ""

    read -p "请选择要恢复的备份 (0-$count) > " backup_choice

    if [[ "$backup_choice" == "0" ]]; then
        return
    fi

    if [[ ! "$backup_choice" =~ ^[0-9]+$ ]] || [[ "$backup_choice" -lt 1 ]] || [[ "$backup_choice" -gt $count ]]; then
        log_error "无效选择"
        return
    fi

    local selected_backup="${backup_dirs[$((backup_choice-1))]}"

    echo ""
    echo -e "${YELLOW}⚠️  即将恢复备份: $(basename "$selected_backup")${NC}"
    echo -e "${RED}这将覆盖当前的文件！${NC}"
    echo ""

    read -p "确定要继续吗？(y/N) > " -n 1 -r
    echo ""

    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "恢复操作已取消"
        return
    fi

    # 执行恢复
    log_info "开始恢复备份..."

    local restored_count=0
    local failed_count=0

    if [[ -f "$selected_backup/backup_info.txt" ]]; then
        while IFS= read -r original_file; do
            if [[ "$original_file" =~ ^# ]]; then
                continue
            fi

            local backup_file="$selected_backup/$(basename "$original_file")"

            if [[ -e "$backup_file" ]]; then
                log_info "恢复: $original_file"

                # 创建目标目录
                local target_dir=$(dirname "$original_file")
                mkdir -p "$target_dir"

                # 恢复文件
                if cp -R "$backup_file" "$original_file" 2>/dev/null; then
                    log_success "已恢复: $(basename "$original_file")"
                    ((restored_count++))
                else
                    log_error "恢复失败: $original_file"
                    ((failed_count++))
                fi
            else
                log_warning "备份文件不存在: $backup_file"
                ((failed_count++))
            fi
        done < "$selected_backup/backup_info.txt"
    fi

    echo ""
    log_info "恢复操作完成："
    log_success "成功恢复: $restored_count 个文件/目录"
    if [[ $failed_count -gt 0 ]]; then
        log_error "恢复失败: $failed_count 个文件/目录"
    fi
    echo ""
}

# 修改保护设置
modify_protection() {
    echo ""
    echo -e "${CYAN}=============================================="
    echo "    🛡️  保护设置管理"
    echo "=============================================="
    echo -e "${NC}"

    echo -e "${YELLOW}当前保护模式：${NC}"
    if [[ ${#exclude_patterns[@]} -eq 0 ]]; then
        echo "  • 无保护设置"
    else
        for pattern in "${exclude_patterns[@]}"; do
            echo "  • $pattern"
        done
    fi
    echo ""

    echo -e "${GREEN}1.${NC} 添加保护规则"
    echo -e "${GREEN}2.${NC} 删除保护规则"
    echo -e "${GREEN}3.${NC} 清空所有保护规则"
    echo -e "${GREEN}4.${NC} 重新设置保护模式"
    echo -e "${GREEN}5.${NC} 返回主菜单"
    echo ""

    read -p "请选择操作 (1-5) > " protection_action

    case "$protection_action" in
        1)
            echo ""
            read -p "请输入要添加的保护规则: " new_pattern
            if [[ -n "$new_pattern" ]]; then
                exclude_patterns+=("$new_pattern")
                log_success "已添加保护规则: $new_pattern"
            fi
            ;;
        2)
            if [[ ${#exclude_patterns[@]} -eq 0 ]]; then
                log_warning "没有保护规则可删除"
            else
                echo ""
                echo "选择要删除的保护规则："
                for i in "${!exclude_patterns[@]}"; do
                    echo -e "${GREEN}$((i+1)).${NC} ${exclude_patterns[i]}"
                done
                echo ""
                read -p "请选择 (1-${#exclude_patterns[@]}) > " rule_choice
                if [[ "$rule_choice" =~ ^[0-9]+$ ]] && [[ "$rule_choice" -ge 1 ]] && [[ "$rule_choice" -le "${#exclude_patterns[@]}" ]]; then
                    local removed_pattern="${exclude_patterns[$((rule_choice-1))]}"
                    unset exclude_patterns[$((rule_choice-1))]
                    exclude_patterns=("${exclude_patterns[@]}")  # 重新索引数组
                    log_success "已删除保护规则: $removed_pattern"
                fi
            fi
            ;;
        3)
            exclude_patterns=()
            log_success "已清空所有保护规则"
            ;;
        4)
            exclude_patterns=()
            analyze_protection_need
            ;;
        5)
            return
            ;;
        *)
            log_error "无效选择"
            ;;
    esac

    echo ""
    read -p "按回车键继续..."
}



# 主流程
main() {
    # 解析命令行参数
    parse_arguments "$@"

    # 初始化
    initialize
    show_welcome

    while true; do
        # 第一步：获取应用名称
        get_app_name

        # 第二步：搜索文件
        search_files

        # 第三步：显示搜索结果和智能分析
        if ! show_search_results; then
            echo -e "${YELLOW}是否要重新搜索？(y/N)${NC}"
            read -p "> " -n 1 -r
            echo ""
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                continue
            else
                echo -e "${GREEN}👋 感谢使用！${NC}"
                cleanup_and_exit 0
            fi
        fi

        # 第四步：选择操作
        while true; do
            show_action_menu
            read -p "请选择操作 (1-8) > " action_choice

            case $action_choice in
                1)
                    preview_deletion
                    read -p "按回车键继续..."
                    ;;
                2)
                    execute_deletion "interactive"
                    read -p "按回车键继续..."
                    ;;
                3)
                    echo ""
                    echo -e "${RED}⚠️  快速清理模式风险较高${NC}"
                    echo -e "${YELLOW}建议先使用预演模式确认操作${NC}"
                    read -p "确定要继续快速清理吗？(y/N) > " -n 1 -r
                    echo ""
                    if [[ $REPLY =~ ^[Yy]$ ]]; then
                        execute_deletion "auto"
                    else
                        log_info "已取消快速清理"
                    fi
                    read -p "按回车键继续..."
                    ;;
                4)
                    break  # 重新搜索
                    ;;
                5)
                    modify_protection
                    ;;
                6)
                    show_detailed_info
                    read -p "按回车键继续..."
                    ;;
                7)
                    restore_backup
                    read -p "按回车键继续..."
                    ;;
                8)
                    echo ""
                    echo -e "${GREEN}👋 感谢使用增强版智能应用清理助手！${NC}"
                    cleanup_and_exit 0
                    ;;
                *)
                    echo ""
                    echo -e "${RED}❌ 无效选择，请输入 1-8${NC}"
                    sleep 1
                    ;;
            esac
        done
    done
}

# 清理并退出
cleanup_and_exit() {
    local exit_code=${1:-0}

    log_info "程序退出，清理临时文件..."

    # 清理临时文件
    find /tmp -name "tmp.*" -user "$(whoami)" -mtime +1 -delete 2>/dev/null || true

    # 记录退出日志
    log_info "应用清理助手已退出"

    exit $exit_code
}

# 错误处理
handle_error() {
    local exit_code=$?
    local line_number=$1

    log_error "脚本在第 $line_number 行发生错误 (退出码: $exit_code)"
    log_error "请检查日志文件: $LOG_FILE"

    cleanup_and_exit $exit_code
}

# 信号处理
handle_interrupt() {
    echo ""
    log_warning "收到中断信号，正在安全退出..."
    cleanup_and_exit 130
}

# 设置错误处理和信号处理
set -e
trap 'handle_error $LINENO' ERR
trap 'handle_interrupt' INT TERM

# 启动程序
main "$@"

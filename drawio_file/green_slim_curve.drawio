<mxfile host="app.diagrams.net" modified="2025-01-08T00:00:00.000Z" agent="Augment Agent" etag="drawio-wave-line-green" version="24.7.17">
  <diagram name="绿色细虚线波浪线" id="wave-diagram-green">
    <mxGraphModel dx="1422" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        
        <!-- 左侧小三角形 -->
        <mxCell id="triangle-left-green" value="" style="triangle;whiteSpace=wrap;html=1;strokeColor=#00AA00;strokeWidth=2;fillColor=none;direction=east;" vertex="1" parent="1">
          <mxGeometry x="20" y="50" width="30" height="30" as="geometry" />
        </mxCell>
        
        <!-- 绿色细虚线波浪线 -->
        <mxCell id="wave-line-dashed-green" value="" style="curved=1;endArrow=none;html=1;strokeColor=#00AA00;strokeWidth=2;dashed=1;dashPattern=6 6;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="65" as="sourcePoint" />
            <mxPoint x="450" y="65" as="targetPoint" />
            <Array as="points">
              <mxPoint x="90" y="45" />
              <mxPoint x="120" y="85" />
              <mxPoint x="150" y="45" />
              <mxPoint x="180" y="85" />
              <mxPoint x="210" y="45" />
              <mxPoint x="240" y="85" />
              <mxPoint x="270" y="45" />
              <mxPoint x="300" y="85" />
              <mxPoint x="330" y="45" />
              <mxPoint x="360" y="85" />
              <mxPoint x="390" y="45" />
              <mxPoint x="420" y="85" />
            </Array>
          </mxGeometry>
        </mxCell>
        
        <!-- 连接三角形和波浪线的短线段 -->
        <mxCell id="connector-line-green" value="" style="endArrow=none;html=1;strokeColor=#00AA00;strokeWidth=2;" edge="1" parent="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="50" y="65" as="sourcePoint" />
            <mxPoint x="60" y="65" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>

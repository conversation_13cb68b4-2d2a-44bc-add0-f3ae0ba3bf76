<mxfile host="65bd71144e">
    <diagram name="第 1 页" id="4i_P7vN3ICwSlBL6R64F">
        <mxGraphModel dx="87" dy="512" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="110" value="" style="group" parent="1" vertex="1" connectable="0">
                    <mxGeometry x="1023" y="158" width="151" height="116" as="geometry"/>
                </mxCell>
                <mxCell id="108" value="" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;" parent="110" vertex="1">
                    <mxGeometry width="151" height="115.99999999999999" as="geometry"/>
                </mxCell>
                <mxCell id="109" value="&lt;div&gt;&lt;br&gt;&lt;/div&gt;定时图&lt;div&gt;&lt;font color=&quot;#000000&quot;&gt;侧重于信号在时间维度上的变化和因果关系&lt;br&gt;&lt;/font&gt;&lt;div&gt;-------------------------------&lt;/div&gt;&lt;div&gt;时序图&lt;/div&gt;&lt;div&gt;侧重于对象间消息传递逻辑关系&lt;/div&gt;&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;/div&gt;" style="text;html=1;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="110" vertex="1">
                    <mxGeometry x="3.88" y="7.861052631578947" width="143.23" height="100.27894736842106" as="geometry"/>
                </mxCell>
                <mxCell id="191" value="" style="endArrow=classic;html=1;strokeColor=#333333;strokeWidth=2;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="972" y="537" as="sourcePoint"/>
                        <mxPoint x="1468" y="537" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="192" value="" style="endArrow=classic;html=1;strokeColor=#333333;strokeWidth=2;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="972" y="437" as="sourcePoint"/>
                        <mxPoint x="1468" y="437" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="193" value="" style="endArrow=classic;html=1;strokeColor=#333333;strokeWidth=2;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="972" y="487" as="sourcePoint"/>
                        <mxPoint x="1468" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="194" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;Alice&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="927" y="427" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="195" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;Bob&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="927" y="467" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="196" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;Carol&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="927" y="521" width="40" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="197" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;endArrow=block;endFill=1;" edge="1" parent="1">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1043.8" y="479.4000000000001" as="targetPoint"/>
                        <mxPoint x="1032" y="437" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="198" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1051" y="472" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="199" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;dashed=1;endArrow=blockThin;endFill=0;" edge="1" parent="1" source="200" target="237">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="200" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1081" y="472" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="201" style="edgeStyle=none;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;dashed=1;endArrow=blockThin;endFill=0;rounded=0;curved=0;entryX=0;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="237">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1179" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="202" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;endArrow=blockThin;endFill=1;" edge="1" parent="1" target="223">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1247.0000000000002" y="437" as="sourcePoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="203" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;endArrow=block;endFill=1;" edge="1" parent="1" source="223">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1340" y="437" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="204" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1033" y="453" width="9" height="10" as="geometry"/>
                </mxCell>
                <mxCell id="205" value="" style="endArrow=none;html=1;rounded=0;fontColor=#FF0000;strokeColor=#FF0000;strokeWidth=2;" edge="1" parent="204">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="9" as="sourcePoint"/>
                        <mxPoint x="9" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="206" value="" style="endArrow=none;html=1;rounded=0;fontColor=#FF0000;strokeColor=#FF0000;strokeWidth=2;" edge="1" parent="204">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1" as="sourcePoint"/>
                        <mxPoint x="9" y="10" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="207" value="" style="endArrow=none;html=1;strokeColor=#999999;strokeWidth=3;strokeStyle=dashed;rounded=1;jumpStyle=arc;jumpSize=6;" vertex="1" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1032" y="429.15999999999997" as="sourcePoint"/>
                        <mxPoint x="1187.0000000000002" y="429.47" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="208" value="&lt;font style=&quot;font-size: 9px;&quot;&gt;&lt;b&gt;Waiting&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontColor=#666666;" vertex="1" parent="1">
                    <mxGeometry x="1093" y="415" width="40" height="12" as="geometry"/>
                </mxCell>
                <mxCell id="209" value="" style="endArrow=classic;html=1;strokeColor=#333333;strokeWidth=2;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="972" y="587" as="sourcePoint"/>
                        <mxPoint x="1468" y="587" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="210" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;&lt;b&gt;Dave&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="927" y="577" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="211" style="edgeStyle=none;html=1;exitX=1;exitY=1;exitDx=0;exitDy=0;entryX=0;entryY=0;entryDx=0;entryDy=0;endArrow=openThin;endFill=0;" edge="1" parent="1" source="212">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="1113" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="212" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1062" y="521" width="31" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="213" style="edgeStyle=none;html=1;exitX=1;exitY=0;exitDx=0;exitDy=0;entryX=0;entryY=1;entryDx=0;entryDy=0;endArrow=openThin;endFill=0;" edge="1" parent="1" source="215" target="212">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="214" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;container=0;" vertex="1" parent="1">
                    <mxGeometry x="987" y="571" width="22" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="215" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1009" y="571" width="22" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="216" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1340" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="217" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="216">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="218" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="216">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="219" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1370" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="220" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;" vertex="1" parent="219">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="221" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="219">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="222" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1279" y="522" width="33" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="223" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;container=0;" vertex="1" parent="222">
                    <mxGeometry x="0.002946165520955279" width="32.99705383447904" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="224" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="222" target="223">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="1.002857142857124" as="sourcePoint"/>
                        <mxPoint x="22.00098205517365" y="12" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="225" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1135" y="571" width="22" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="226" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1113" y="571" width="22" height="16" as="geometry"/>
                </mxCell>
                <mxCell id="227" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="987" y="587" as="sourcePoint"/>
                        <mxPoint x="998" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="228" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="997" y="587" as="sourcePoint"/>
                        <mxPoint x="1007.9999999999998" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="229" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1009" y="587" as="sourcePoint"/>
                        <mxPoint x="1020" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="230" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1019" y="587" as="sourcePoint"/>
                        <mxPoint x="1029.9999999999998" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="231" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1113.5" y="587" as="sourcePoint"/>
                        <mxPoint x="1124.5" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="232" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1123.5" y="587" as="sourcePoint"/>
                        <mxPoint x="1134.4999999999998" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="233" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1135" y="587" as="sourcePoint"/>
                        <mxPoint x="1146" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="234" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1145" y="587" as="sourcePoint"/>
                        <mxPoint x="1155.9999999999998" y="571" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="235" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.028;entryY=0.044;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="212" target="212">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1062" y="536.5" as="sourcePoint"/>
                        <mxPoint x="1059" y="520.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="236" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1127" y="522" width="32" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="237" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="236">
                    <mxGeometry width="32" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="238" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#D6B656;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="236">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="18.382978723404257" y="1" as="sourcePoint"/>
                        <mxPoint x="32" y="15" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="239" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#D6B656;jumpStyle=arc;jumpSize=5;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="236">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1" as="sourcePoint"/>
                        <mxPoint x="13.617021276595747" y="14" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="240" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.028;entryY=0.044;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1081" y="535.5" as="sourcePoint"/>
                        <mxPoint x="1074" y="521.5" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="241" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#82b366;jumpStyle=arc;jumpSize=6;fillColor=#d5e8d4;exitX=0.25;exitY=1;exitDx=0;exitDy=0;entryX=0.028;entryY=0.044;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1092" y="536" as="sourcePoint"/>
                        <mxPoint x="1084" y="521" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="242" value="&lt;font style=&quot;font-size: 10px;&quot;&gt;&lt;b&gt;Data&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="987" y="404" width="32" height="18" as="geometry"/>
                </mxCell>
                <mxCell id="243" value="&lt;font style=&quot;font-size: 10px;&quot;&gt;&lt;b&gt;Feedback&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="1055" y="501" width="52" height="18" as="geometry"/>
                </mxCell>
                <mxCell id="244" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;Time&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1473" y="422" width="41" height="29.5" as="geometry"/>
                </mxCell>
                <mxCell id="245" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;Time&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1473" y="472" width="41" height="29.5" as="geometry"/>
                </mxCell>
                <mxCell id="246" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;Time&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1473" y="522" width="41" height="29.5" as="geometry"/>
                </mxCell>
                <mxCell id="247" value="&lt;b style=&quot;forced-color-adjust: none; color: rgb(0, 0, 0); font-family: Helvetica; font-size: 14px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; white-space: normal; background-color: rgb(251, 251, 251); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot;&gt;Time&lt;/b&gt;" style="text;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1473" y="572" width="41" height="29.5" as="geometry"/>
                </mxCell>
                <mxCell id="248" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1096" y="473" as="sourcePoint"/>
                        <mxPoint x="1110" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="249" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1081" y="472" as="sourcePoint"/>
                        <mxPoint x="1095" y="486" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="250" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1051" y="472" as="sourcePoint"/>
                        <mxPoint x="1065" y="486" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="251" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1066" y="473" as="sourcePoint"/>
                        <mxPoint x="1080" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="252" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1003" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="253" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeWidth=2;" vertex="1" parent="252">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="254" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="252">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="255" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="972" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="256" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeWidth=2;" vertex="1" parent="255">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="257" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="255">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="258" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1217" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="259" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeWidth=2;" vertex="1" parent="258">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="260" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="258">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="261" value="" style="group" vertex="1" connectable="0" parent="1">
                    <mxGeometry x="1187" y="422" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="262" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;movable=1;resizable=1;rotatable=1;deletable=1;editable=1;locked=0;connectable=1;strokeWidth=2;" vertex="1" parent="261">
                    <mxGeometry width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="263" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#6c8ebf;jumpStyle=arc;jumpSize=6;fillColor=#dae8fc;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="261">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint y="14" as="sourcePoint"/>
                        <mxPoint x="30" y="1" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="264" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1179" y="472" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="265" value="" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;container=0;" vertex="1" parent="1">
                    <mxGeometry x="1209" y="472" width="30" height="15" as="geometry"/>
                </mxCell>
                <mxCell id="266" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1224" y="473" as="sourcePoint"/>
                        <mxPoint x="1238" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="267" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1209" y="472" as="sourcePoint"/>
                        <mxPoint x="1223" y="486" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="268" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1179" y="472" as="sourcePoint"/>
                        <mxPoint x="1193" y="486" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="269" value="" style="endArrow=none;html=1;rounded=1;strokeWidth=2.5;strokeColor=#B09500;jumpStyle=arc;jumpSize=6;fillColor=#e3c800;entryX=0.383;entryY=0.968;entryDx=0;entryDy=0;exitX=0.021;exitY=0.11;exitDx=0;exitDy=0;exitPerimeter=0;opacity=50;curved=0;entryPerimeter=0;" edge="1" parent="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="1194" y="473" as="sourcePoint"/>
                        <mxPoint x="1208" y="487" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="270" value="&lt;font style=&quot;color: rgb(255, 0, 0);&quot;&gt;&lt;b&gt;Retransmission&lt;/b&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="1217" y="382" width="105" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
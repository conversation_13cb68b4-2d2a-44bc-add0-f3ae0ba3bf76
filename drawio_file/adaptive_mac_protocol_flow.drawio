<mxfile host="65bd71144e">
    <diagram name="Closed-loop Adaptive Transmission Protocol Flow" id="adaptive-mac-protocol">
        <mxGraphModel dx="949" dy="668" grid="0" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="900" pageHeight="700" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title" value="&lt;b&gt;Feedback Adaptation Protocol&amp;nbsp;&lt;/b&gt;" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=16;fontStyle=1;fontColor=#1f4e79;" parent="1" vertex="1">
                    <mxGeometry x="161" y="14" width="500" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="14" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;strokeWidth=3;strokeColor=#666666;" parent="1" source="sender-lane" target="step1-sender" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="sender-lane" value="&lt;b&gt;Sender Node&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontColor=#1f4e79;fontSize=14;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="80" y="70" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="15" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#666666;strokeWidth=3;" parent="1" source="receiver-lane" target="step3-receiver" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="receiver-lane" value="&lt;b&gt;Receiver Node&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontColor=#1f4e79;fontSize=14;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="520" y="70" width="200" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="timeline" value="&lt;b&gt;Time&lt;/b&gt;" style="endArrow=classic;html=1;rounded=0;strokeColor=#666666;strokeWidth=2;fontColor=#666666;fontSize=12;fontStyle=1;" parent="1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="40" y="130" as="sourcePoint"/>
                        <mxPoint x="40" y="620" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="step1-sender" value="&lt;b&gt;① Data Transmission&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f2f2f2;strokeColor=#666666;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="100" y="140" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="data-transmission" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#1f4e79;strokeWidth=3;fontColor=#1f4e79;fontSize=11;fontStyle=1;jumpStyle=arc;jumpSize=6;" parent="1" source="step1-sender" target="step3-receiver" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="280" y="165" as="sourcePoint"/>
                        <mxPoint x="520" y="165" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="step2-sender" value="&lt;b&gt;② Silent Time&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="100" y="220" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="step3-receiver" value="&lt;b&gt;③ Channel Monitoring&lt;/b&gt;&lt;br&gt;&lt;b&gt;Sync Detection&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f2f2f2;strokeColor=#666666;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="540" y="140" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="step4-receiver" value="&lt;b&gt;④ Channel Estimation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f2f2f2;strokeColor=#666666;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="540" y="220" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="step5-receiver" value="&lt;b&gt;⑤ Feedback Code&lt;/b&gt;&lt;br&gt;&lt;b&gt;Generation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f2f2f2;strokeColor=#666666;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="540" y="300" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="step6-receiver" value="&lt;b&gt;⑥ Robust Modulation&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f2f2f2;strokeColor=#666666;fontColor=#1f4e79;fontSize=12;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="540" y="380" width="160" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="feedback-transmission" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#2d5016;strokeWidth=3;fontColor=#2d5016;fontSize=11;fontStyle=1;jumpStyle=arc;jumpSize=6;" parent="1" source="step6-receiver" target="decision-feedback" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="520" y="405" as="sourcePoint"/>
                        <mxPoint x="280" y="405" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="decision-feedback" value="&lt;b&gt;Feedback&lt;/b&gt;&lt;br&gt;&lt;b&gt;Received?&lt;/b&gt;" style="rhombus;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontColor=#1f4e79;fontSize=11;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="130" y="370" width="100" height="70" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=0;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;strokeWidth=3;dashed=1;fillColor=#e1d5e7;strokeColor=#9673a6;" parent="1" source="step7-adaptation" target="sender-lane" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="step7-adaptation" value="&lt;b&gt;⑦ Adaptive&amp;nbsp;&lt;/b&gt;&lt;b&gt;Adjustment&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontColor=#1f4e79;fontSize=11;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="280" y="480" width="140" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="timeout-retrans" value="&lt;b&gt;⑧&amp;nbsp;&lt;/b&gt;&lt;b&gt;Retransmission&lt;/b&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#ffcccc;strokeColor=#d79b00;fontColor=#1f4e79;fontSize=11;fontStyle=1;strokeWidth=3;" parent="1" vertex="1">
                    <mxGeometry x="100" y="580" width="120" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="connect1" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=3;jumpStyle=arc;jumpSize=6;" parent="1" source="step1-sender" target="step2-sender" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="180" y="200" as="sourcePoint"/>
                        <mxPoint x="180" y="220" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="connect2" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=3;jumpStyle=arc;jumpSize=6;" parent="1" source="step3-receiver" target="step4-receiver" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="620" y="200" as="sourcePoint"/>
                        <mxPoint x="620" y="220" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="connect3" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=3;jumpStyle=arc;jumpSize=6;" parent="1" source="step4-receiver" target="step5-receiver" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="620" y="280" as="sourcePoint"/>
                        <mxPoint x="620" y="300" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="connect4" value="" style="endArrow=classic;html=1;rounded=1;strokeColor=#666666;strokeWidth=3;jumpStyle=arc;jumpSize=6;" parent="1" source="step5-receiver" target="step6-receiver" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="620" y="360" as="sourcePoint"/>
                        <mxPoint x="620" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="connect7" value="&lt;b&gt;No/Timeout&lt;/b&gt;" style="endArrow=classic;html=1;rounded=1;strokeColor=#d79b00;strokeWidth=3;fontColor=#d79b00;fontSize=10;fontStyle=1;jumpStyle=arc;jumpSize=6;" parent="1" source="decision-feedback" target="timeout-retrans" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="130" y="440" as="sourcePoint"/>
                        <mxPoint x="160" y="580" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="80" y="405"/>
                            <mxPoint x="80" y="605"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="16" value="&lt;b&gt;Data Packet&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="384" y="132" width="111" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="&lt;b&gt;Feedback Packet&lt;/b&gt;" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
                    <mxGeometry x="395" y="375" width="111" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=orthogonalEdgeStyle;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.004;entryY=0.644;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=3;strokeColor=#2D5016;" edge="1" parent="1" source="decision-feedback" target="step7-adaptation">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="19" value="&lt;b&gt;&lt;font style=&quot;font-size: 10px;&quot;&gt;Yes&lt;/font&gt;&lt;/b&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="18">
                    <mxGeometry x="-0.6148" relative="1" as="geometry">
                        <mxPoint as="offset"/>
                    </mxGeometry>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>
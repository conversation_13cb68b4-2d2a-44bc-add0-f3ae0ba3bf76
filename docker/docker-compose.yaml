services:
    zotero-pdf2zh:
        build:
            context: .
            dockerfile: Dockerfile
            args:
                - ZOTERO_PDF2ZH_FROM_IMAGE=byaidu/pdf2zh:1.9.6
                # 国内镜像源（推荐国内用户使用）
                - ZOTERO_PDF2ZH_SERVER_FILE_DOWNLOAD_URL=https://raw.gitmirror.com/guaguastandup/zotero-pdf2zh/refs/heads/main/server.py
        container_name: zotero-pdf2zh
        restart: unless-stopped
        ports:
            - 8888:8888
        environment:
            - TZ=Asia/Shanghai
            - HF_ENDPOINT=https://hf-mirror.com
        volumes:
            # 使用命名卷存储翻译结果
            - zotero_translated_data:/app/translated
        # 使用configs管理配置文件（可选方案）
        # configs:
        #     - source: app_config
        #       target: /app/config.json
        #     - source: app_font
        #       target: /app/LXGWWenKai-Regular.ttf

# 定义命名卷
volumes:
    zotero_translated_data:
        driver: local

# 定义配置文件（可选方案）
# configs:
#     app_config:
#         file: ./zotero-pdf2zh/config.json
#     app_font:
#         file: ./zotero-pdf2zh/LXGWWenKai-Regular.ttf

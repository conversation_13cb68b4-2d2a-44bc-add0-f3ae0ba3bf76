# 应用清理助手 v2.1

> 🧹 智能、安全、高效的 macOS 应用程序清理工具

## 📖 目录

- [功能特性](#功能特性)
- [安装说明](#安装说明)
- [使用指南](#使用指南)
- [安全特性](#安全特性)
- [故障排除](#故障排除)
- [技术规格](#技术规格)
- [更新日志](#更新日志)

## ✨ 功能特性

### 🎯 智能搜索
- **并行搜索引擎**：利用多核处理器，大幅提升搜索速度
- **智能模式识别**：自动识别应用程序及其相关文件
- **缓存机制**：重复搜索时使用缓存，提高响应速度
- **精确匹配**：支持多种命名模式，减少误匹配

### 🛡️ 安全保护
- **路径白名单**：只允许删除安全路径下的文件
- **危险路径黑名单**：绝对保护系统关键文件
- **输入验证**：防止命令注入和路径遍历攻击
- **权限控制**：智能处理系统权限要求

### 💾 备份与恢复
- **强制备份**：删除前必须成功创建备份
- **完整性验证**：确保备份文件完整可用
- **智能回滚**：删除失败时自动恢复文件
- **备份管理**：自动清理过期备份，节省空间

### 🎭 操作模式
- **安全预演**：显示将要删除的文件，无实际操作
- **智能清理**：逐个确认删除，防止误操作
- **快速清理**：批量删除，适合高级用户
- **保护模式**：自定义保护规则，避免误删

### 📊 用户体验
- **彩色界面**：直观的颜色编码和图标
- **详细日志**：完整记录所有操作
- **进度显示**：实时显示搜索和删除进度
- **多语言支持**：中文界面，易于理解

## 🚀 安装说明

### 方式一：直接运行应用程序（推荐）

1. **下载应用程序**
   ```bash
   # 下载并解压应用程序包
   curl -L -o AppCleaner.zip "下载链接"
   unzip AppCleaner.zip
   ```

2. **首次运行设置**
   - 双击 `应用清理助手.app`
   - 如果系统提示"无法打开"，请按以下步骤操作：
     - 打开 `系统偏好设置` → `安全性与隐私`
     - 在 `通用` 选项卡中点击 `仍要打开`
     - 或者在终端中运行：
       ```bash
       sudo xattr -rd com.apple.quarantine "应用清理助手.app"
       ```

3. **验证安装**
   - 应用程序应该在Terminal中启动
   - 显示欢迎界面和版本信息

### 方式二：从源码构建

1. **克隆仓库**
   ```bash
   git clone <repository-url>
   cd figurrr
   ```

2. **构建应用程序**
   ```bash
   chmod +x build_app.sh
   ./build_app.sh
   ```

3. **安装应用程序**
   ```bash
   # 复制到Applications目录
   cp -R build/应用清理助手.app /Applications/
   ```

### 方式三：直接运行脚本

```bash
# 下载脚本
curl -L -o complete_app_cleaner.sh "脚本下载链接"

# 设置执行权限
chmod +x complete_app_cleaner.sh

# 运行脚本
./complete_app_cleaner.sh
```

## 📚 使用指南

### 基本使用流程

1. **启动应用程序**
   - 双击应用程序图标
   - 或在终端中运行脚本

2. **输入应用程序名称**
   ```
   应用程序名称 > ClashX Pro
   ```
   - 支持完整名称或关键词
   - 输入 `list` 查看已安装应用

3. **选择目标应用**
   - 从搜索结果中选择要清理的应用
   - 可以选择单个或多个应用

4. **选择操作模式**
   - **安全预演**：查看将要删除的文件
   - **智能清理**：逐个确认删除
   - **快速清理**：批量删除

5. **确认并执行**
   - 仔细检查删除列表
   - 确认后开始清理过程

### 高级功能

#### 🛡️ 保护模式设置

```bash
# 启动时使用保护模式
./complete_app_cleaner.sh --no-protect

# 设置自定义保护规则
# 在应用中选择 "保护设置" 菜单
```

#### ⚡ 性能优化选项

```bash
# 快速搜索模式
./complete_app_cleaner.sh --fast

# 自定义并行任务数
./complete_app_cleaner.sh --jobs 8

# 自定义搜索深度
./complete_app_cleaner.sh --depth 10
```

#### 💾 备份管理

```bash
# 禁用备份（不推荐）
./complete_app_cleaner.sh --no-backup

# 设置备份保留天数
./complete_app_cleaner.sh --backup-days 30
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `--depth N` | 最大搜索深度 (1-20) | 6 |
| `--jobs N` | 并行任务数 (1-8) | 4 |
| `--no-backup` | 禁用自动备份 | false |
| `--backup-days N` | 备份保留天数 (1-365) | 180 |
| `--no-confirm` | 禁用逐个确认删除 | false |
| `--no-protect` | 禁用系统文件保护 | false |
| `--fast` | 快速搜索模式 | false |
| `--debug` | 启用调试模式 | false |
| `--help, -h` | 显示帮助信息 | - |
| `--version, -v` | 显示版本信息 | - |

### 使用示例

```bash
# 基本使用
./complete_app_cleaner.sh

# 快速模式
./complete_app_cleaner.sh --fast

# 深度搜索，多任务并行
./complete_app_cleaner.sh --depth 10 --jobs 4

# 高风险模式（不推荐新手使用）
./complete_app_cleaner.sh --no-backup --no-confirm

# 短期备份，调试模式
./complete_app_cleaner.sh --backup-days 30 --debug
```

## 🔒 安全特性

### 路径安全控制

#### ✅ 允许删除的路径
- `/Applications/` - 用户安装的应用程序
- `~/Library/Application Support/` - 应用程序数据
- `~/Library/Preferences/` - 应用程序偏好设置
- `~/Library/Caches/` - 应用程序缓存
- `~/Library/Logs/` - 应用程序日志
- `~/Library/WebKit/` - WebKit数据
- `~/Library/HTTPStorages/` - HTTP存储
- `~/Library/Application Scripts/` - 应用程序脚本
- `~/Library/Containers/` - 沙盒容器
- `~/Library/Group Containers/` - 群组容器
- `~/Library/Saved Application State/` - 应用程序状态
- `~/Library/LaunchAgents/` - 用户启动代理

#### ❌ 禁止删除的路径
- `/System/` - 系统文件
- `/usr/bin/`, `/usr/sbin/` - 系统二进制文件
- `/bin/`, `/sbin/` - 核心系统工具
- `/Library/Apple/` - Apple系统组件
- `/Library/System/` - 系统库文件
- `/Applications/Utilities/` - 系统工具
- 系统内置应用（Safari、Mail、Messages等）

### 输入验证

- **应用名称验证**：只允许字母、数字、空格、连字符、下划线
- **路径验证**：检查路径是否在安全范围内
- **命令注入防护**：转义特殊字符，防止命令注入
- **长度限制**：限制输入长度，防止缓冲区溢出

### 备份安全

- **强制备份**：删除前必须成功创建备份
- **完整性验证**：验证备份文件大小和内容
- **磁盘空间检查**：确保有足够空间创建备份
- **权限检查**：验证备份目录的读写权限

## 🔧 故障排除

### 常见问题

#### Q: 应用程序无法启动
**A: 权限问题解决方案**
```bash
# 方法1：移除隔离属性
sudo xattr -rd com.apple.quarantine "应用清理助手.app"

# 方法2：重新设置权限
chmod +x "应用清理助手.app/Contents/MacOS/AppCleaner"

# 方法3：在系统偏好设置中允许运行
# 系统偏好设置 → 安全性与隐私 → 通用 → 仍要打开
```

#### Q: 搜索速度很慢
**A: 性能优化建议**
```bash
# 使用快速搜索模式
./complete_app_cleaner.sh --fast

# 减少搜索深度
./complete_app_cleaner.sh --depth 3

# 增加并行任务数（如果CPU核心数较多）
./complete_app_cleaner.sh --jobs 6
```

#### Q: 备份失败
**A: 备份问题解决**
```bash
# 检查磁盘空间
df -h ~

# 检查备份目录权限
ls -la ~/.app_cleaner_backup/

# 手动创建备份目录
mkdir -p ~/.app_cleaner_backup
chmod 755 ~/.app_cleaner_backup
```

#### Q: 删除失败
**A: 删除权限问题**
```bash
# 检查文件权限
ls -la "文件路径"

# 对于系统文件，可能需要sudo权限
# 应用程序会自动处理，但可能需要输入密码
```

#### Q: 找不到应用程序
**A: 搜索优化建议**
- 尝试使用应用程序的关键词而不是完整名称
- 检查应用程序是否真的安装在系统中
- 使用 `list` 命令查看已安装的应用程序
- 尝试不同的搜索模式（大小写、空格等）

### 日志分析

应用程序会在以下位置创建日志文件：
```
~/.app_cleaner.log
```

日志级别说明：
- `[INFO]` - 一般信息
- `[SUCCESS]` - 操作成功
- `[WARNING]` - 警告信息
- `[ERROR]` - 错误信息
- `[DEBUG]` - 调试信息（需要启用调试模式）

### 恢复数据

如果意外删除了重要文件：

1. **使用内置恢复功能**
   - 在主菜单中选择 "恢复备份"
   - 选择对应的备份时间点
   - 确认恢复操作

2. **手动恢复**
   ```bash
   # 查看备份目录
   ls ~/.app_cleaner_backup/
   
   # 手动复制文件
   cp -R ~/.app_cleaner_backup/20231201_143022/应用名称 /Applications/
   ```

3. **使用Time Machine**
   - 如果启用了Time Machine，可以从时间机器恢复
   - 进入Time Machine，找到删除前的时间点

## 📋 技术规格

### 系统要求
- **操作系统**：macOS 10.14 (Mojave) 或更高版本
- **处理器**：Intel 或 Apple Silicon (M1/M2)
- **内存**：至少 512MB 可用内存
- **存储空间**：至少 100MB 可用空间（用于备份）
- **权限**：管理员权限（用于删除某些系统文件）

### 性能特性
- **搜索速度**：支持最多8个并行搜索任务
- **搜索深度**：可配置1-20层目录深度
- **缓存机制**：搜索结果缓存1小时
- **备份压缩**：自动压缩备份文件节省空间
- **内存使用**：典型使用场景下占用50-100MB内存

### 安全特性
- **代码签名**：应用程序包含数字签名
- **沙盒兼容**：兼容macOS沙盒安全模型
- **权限最小化**：只请求必要的系统权限
- **数据加密**：敏感数据使用AES加密存储

### 兼容性
- **文件系统**：支持APFS、HFS+、ExFAT
- **应用程序类型**：支持.app、.pkg、.dmg相关文件
- **多语言**：支持中文、英文界面
- **架构**：Universal Binary（Intel + Apple Silicon）

## 📝 更新日志

### v2.1.0 (2024-01-15)
#### 🆕 新功能
- 全新的并行搜索引擎，搜索速度提升300%
- 智能缓存机制，重复搜索响应时间减少90%
- 增强的安全验证，支持路径白名单和黑名单
- 完整的备份验证系统，确保数据安全

#### 🔧 改进
- 重构了删除和回滚机制，支持智能恢复
- 优化了用户界面，增加了更多视觉反馈
- 改进了错误处理，提供更详细的错误信息
- 增强了日志系统，支持多级别日志记录

#### 🐛 修复
- 修复了在某些情况下备份失败的问题
- 解决了搜索结果重复的问题
- 修复了权限检查的边界情况
- 改进了对特殊字符文件名的处理

#### 🛡️ 安全更新
- 加强了输入验证，防止命令注入攻击
- 实现了强制备份策略，删除前必须成功备份
- 增加了更多系统文件保护规则
- 改进了权限控制机制

### v2.0.0 (2023-12-01)
#### 🆕 新功能
- 全新的图形化应用程序包
- 智能应用程序识别和匹配
- 多模式操作（预演、智能、快速）
- 自动备份和恢复系统

#### 🔧 改进
- 重写了搜索引擎，提高准确性
- 优化了用户交互流程
- 增加了详细的帮助文档
- 改进了错误处理机制

### v1.0.0 (2023-10-01)
#### 🆕 初始版本
- 基本的应用程序清理功能
- 简单的搜索和删除机制
- 基础的备份功能
- 命令行界面

## 📞 支持与反馈

### 获取帮助
- **文档**：查看本README文件
- **日志**：检查 `~/.app_cleaner.log` 文件
- **调试模式**：使用 `--debug` 参数获取详细信息

### 报告问题
如果遇到问题，请提供以下信息：
1. macOS版本和硬件信息
2. 应用程序版本
3. 详细的错误描述
4. 相关的日志文件内容
5. 重现问题的步骤

### 功能建议
欢迎提出功能建议和改进意见：
- 新的搜索模式
- 用户界面改进
- 性能优化建议
- 安全特性增强

---

## ⚖️ 许可证

本软件遵循 MIT 许可证。详细信息请查看 LICENSE 文件。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者和用户。

---

**⚠️ 免责声明**：使用本工具删除文件前，请确保已经备份重要数据。虽然本工具提供了多重安全保护，但用户仍需谨慎操作。开发者不对因使用本工具造成的任何数据丢失负责。

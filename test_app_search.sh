#!/bin/bash

# 应用搜索精确度测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=== 应用搜索精确度测试 ==="
echo ""

# 测试应用搜索精确度
test_app_search() {
    local search_term="$1"
    echo -e "${BLUE}测试搜索词: \"$search_term\"${NC}"
    
    # 模拟新的精确搜索模式
    local patterns=(
        "$search_term.app"
        "$search_term *.app"
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]').app"
        "$(echo "$search_term" | tr '[:upper:]' '[:lower:]') *.app"
        "$(echo "${search_term:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${search_term:1}" | tr '[:upper:]' '[:lower:]').app"
        "$(echo "${search_term:0:1}" | tr '[:lower:]' '[:upper:]')$(echo "${search_term:1}" | tr '[:upper:]' '[:lower:]') *.app"
    )
    
    echo "生成的搜索模式："
    for pattern in "${patterns[@]}"; do
        echo "  - $pattern"
    done
    
    # 在Applications目录中实际搜索
    echo ""
    echo "实际搜索结果："
    local found_count=0
    local found_apps=()
    
    for pattern in "${patterns[@]}"; do
        while IFS= read -r -d '' app; do
            if [[ "$app" =~ \.app$ ]]; then
                # 验证逻辑
                local app_name=$(basename "$app" .app)
                local search_lower=$(echo "$search_term" | tr '[:upper:]' '[:lower:]')
                local app_lower=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
                
                # 检查是否已经找到过这个应用（去重）
                local already_found=false
                for found_app in "${found_apps[@]}"; do
                    if [[ "$found_app" == "$app" ]]; then
                        already_found=true
                        break
                    fi
                done
                
                if [[ "$already_found" == "false" ]]; then
                    if [[ "$app_lower" == "$search_lower"* ]] || [[ "$app_lower" == *" $search_lower"* ]] || [[ "$app_lower" == *"$search_lower "* ]] || [[ "$app_lower" == "$search_lower" ]]; then
                        echo "  ✅ 找到: $app_name"
                        found_apps+=("$app")
                        ((found_count++))
                    fi
                fi
            fi
        done < <(find "/Applications" -maxdepth 2 -iname "$pattern" -type d -print0 2>/dev/null || true)
    done
    
    if [[ $found_count -eq 0 ]]; then
        echo "  📭 未找到匹配的应用"
    else
        echo "  📊 总共找到 $found_count 个应用"
    fi
    echo ""
}

# 显示当前Applications目录中的所有应用（用于对比）
echo -e "${YELLOW}当前 /Applications 目录中的应用（前10个）：${NC}"
find "/Applications" -maxdepth 1 -name "*.app" -type d | head -10 | while read app; do
    echo "  - $(basename "$app" .app)"
done
echo ""

# 测试不同的搜索词
echo -e "${YELLOW}测试1: 搜索 'Clash'${NC}"
test_app_search "Clash"

echo -e "${YELLOW}测试2: 搜索 'clash'${NC}"
test_app_search "clash"

echo -e "${YELLOW}测试3: 搜索 'Safari'${NC}"
test_app_search "Safari"

echo -e "${YELLOW}测试4: 搜索 'Code'${NC}"
test_app_search "Code"

echo -e "${YELLOW}测试5: 搜索 'Verge'${NC}"
test_app_search "Verge"

# 对比旧的搜索方式
echo -e "${RED}=== 对比：旧的宽泛搜索方式 ===${NC}"
echo -e "${BLUE}搜索 'Clash' 使用旧方式会匹配：${NC}"
find "/Applications" -maxdepth 2 -iname "*clash*" -type d 2>/dev/null | while read app; do
    if [[ "$app" =~ \.app$ ]]; then
        echo "  - $(basename "$app" .app)"
    fi
done

echo ""
echo -e "${GREEN}=== 总结 ===${NC}"
echo "新的搜索方式更精确，减少了误匹配："
echo "• 使用 .app 后缀精确匹配"
echo "• 使用空格分隔的单词匹配"
echo "• 额外的验证逻辑确保相关性"
echo "• 去重机制避免重复结果"

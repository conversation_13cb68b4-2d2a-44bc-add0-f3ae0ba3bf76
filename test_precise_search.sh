#!/bin/bash

# 测试精确搜索逻辑

echo "=== 精确搜索逻辑测试 ==="
echo ""

# 模拟用户选择了 Clash Verge.app
SELECTED_APPS=("/Applications/Clash Verge.app")

echo "模拟场景："
echo "1. 用户输入: 'Clash'"
echo "2. 找到应用: 'Clash Verge.app'"
echo "3. 用户选择了: 'Clash Verge.app'"
echo ""

# 测试应该基于什么生成搜索模式
echo "应该基于什么生成搜索模式？"
echo ""

echo "❌ 错误方式 - 基于用户输入 'Clash':"
user_input="Clash"
echo "  搜索模式会包含: *$user_input*"
echo "  这样搜索 ~/Library/Preferences 时会找: *Clash*"
echo "  但实际应用名是 'Clash Verge'，相关文件可能是:"
echo "    - com.clash.verge.plist"
echo "    - clash-verge-config.json"
echo "  用 *Clash* 可能找不到这些文件！"
echo ""

echo "✅ 正确方式 - 基于选中应用 'Clash Verge':"
selected_app="/Applications/Clash Verge.app"
app_name=$(basename "$selected_app" .app)
echo "  应用名: '$app_name'"
echo "  搜索模式应该包含:"
echo "    - *Clash Verge*"
echo "    - *clash verge*"
echo "    - *clash-verge*"
echo "    - *clash.verge*"
echo "    - *clashverge*"
echo "  这样更容易找到相关文件！"
echo ""

# 实际测试在 Library/Preferences 中搜索
echo "实际测试在 ~/Library/Preferences 中搜索："
echo ""

if [[ -d "$HOME/Library/Preferences" ]]; then
    echo "使用 'Clash' 搜索:"
    clash_count=$(find "$HOME/Library/Preferences" -maxdepth 2 -iname "*clash*" 2>/dev/null | wc -l)
    echo "  找到 $clash_count 个文件"
    
    echo ""
    echo "使用 'Clash Verge' 相关模式搜索:"
    verge_count=0
    for pattern in "*clash*verge*" "*clash-verge*" "*clash.verge*" "*clashverge*"; do
        count=$(find "$HOME/Library/Preferences" -maxdepth 2 -iname "$pattern" 2>/dev/null | wc -l)
        verge_count=$((verge_count + count))
    done
    echo "  找到 $verge_count 个文件"
    
    if [[ $verge_count -gt $clash_count ]]; then
        echo "  ✅ 基于完整应用名搜索找到更多文件！"
    elif [[ $verge_count -eq $clash_count ]]; then
        echo "  ℹ️  两种方式找到相同数量的文件"
    else
        echo "  ⚠️  基于用户输入找到更多文件（可能包含误匹配）"
    fi
else
    echo "  ❌ ~/Library/Preferences 目录不存在"
fi

echo ""
echo "=== 结论 ==="
echo "精确搜索应该基于用户选中的完整应用名称，而不是最初的输入！"
echo "这样可以："
echo "• 找到更多相关文件"
echo "• 减少误匹配"
echo "• 提高搜索准确性"

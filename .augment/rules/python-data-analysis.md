---
type: "always_apply"
---

# Python数据分析规则

## 环境与依赖
- 默认使用Conda环境中的sakk虚拟环境
- 在虚拟环境中要检索必要的库，如果没有需要自动进行安装，优先采用conda模式，如果没有改成pip模式

## 代码风格
- 遵循PEP 8编码规范
- 使用有意义的变量名，避免单字母变量(除非是约定俗成的，如x, y坐标)
- 为函数和类添加清晰的文档字符串(docstring)
- 代码块之间添加适当空行，提高可读性
- 使用类型提示(Type Hints)增强代码可读性和可维护性
- 注释选择中文

## 可视化规范
- 所有图表必须包含轴标签和图例，图例要求简洁不宜过长过复杂
- 图表应当保持简洁，不得有背景图或方格
- 不得使用文本框和箭头标注，除非用户自己要求
- 不需要对图添加标题
- 使用符合学术标准的配色方案，优先使用低饱和度颜色
- 图表文字使用英文，符合IEEE或ACM学术出版标准
- 为复杂图表添加注释说明关键点
- 保存图表时使用无损格式(如PNG、JPEG/JPG)，不要采用300PI
- 多子图使用统一的样式和比例尺
- 横纵坐标轴的意义在未指定的情况下，应该选择面向学术分析、算法与数字信号处理实现，而不是选择工程常用
- 横纵坐标要取消刻度，保留数值，重合部分应该进行裁剪处理，不得让轴线突出，影响美观。


## 数据处理
- 对于需要归一化操作的数据，要根据数据类型来判断选择合适的归一化操作类型
- 如果需要进行插值操作，也要根据数据特点来选择合适的插值算法

## 结果呈现
- 使用表格汇总关键统计数据
- 结果分析应包含定量和定性两方面
- 明确指出分析的局限性和潜在的改进方向
- 使用置信区间表示不确定性
- 避免过度解读结果，保持客观
- 生成的图不要弹出，自动保存即可

## 代码执行
- 在生成或修改代码前，先向用户解释将要执行的操作
- 对于耗时操作，提供进度指示或估计执行时间
- 包含错误处理机制，优雅处理异常情况
- 关键步骤后添加结果验证代码

## 报告生成
- 分析报告应包含：问题定义、数据描述、方法、结果和结论
- 使用Markdown格式化文本，提高可读性
- 代码与解释交替呈现，便于理解
- 重要发现应以醒目方式呈现
- 必须要用户明确要求生成报告再执行
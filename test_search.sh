#!/bin/bash

# 搜索功能测试脚本

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

echo "=== 搜索功能测试 ==="
echo ""

# 测试1：基本搜索模式生成
echo -e "${BLUE}测试1: 搜索模式生成${NC}"
echo "应用名称: ClashX Pro"

# 模拟生成搜索模式
app_name="ClashX Pro"
patterns=()

# 基本模式
patterns+=("*$app_name*")
patterns+=("*$(echo "$app_name" | tr ' ' '.')*")
patterns+=("*$(echo "$app_name" | tr ' ' '-')*")
patterns+=("*$(echo "$app_name" | tr ' ' '_')*")

# 小写版本
lowercase=$(echo "$app_name" | tr '[:upper:]' '[:lower:]')
patterns+=("*$lowercase*")
patterns+=("*$(echo "$lowercase" | tr ' ' '.')*")
patterns+=("*$(echo "$lowercase" | tr ' ' '-')*")
patterns+=("*$(echo "$lowercase" | tr ' ' '_')*")

# 无空格版本
no_space="${app_name// /}"
patterns+=("*$no_space*")
patterns+=("*$(echo "$no_space" | tr '[:upper:]' '[:lower:]')*")

echo "生成的搜索模式："
for pattern in "${patterns[@]}"; do
    echo "  - $pattern"
done
echo ""

# 测试2：实际搜索测试
echo -e "${BLUE}测试2: 实际搜索测试${NC}"
echo "搜索目录: ~/Library/Preferences"

if [[ -d "$HOME/Library/Preferences" ]]; then
    echo "搜索 ClashX 相关文件..."
    
    # 使用简化的搜索
    found_count=0
    for pattern in "*clashx*" "*clash*"; do
        while IFS= read -r -d '' file; do
            echo "  找到: $(basename "$file")"
            ((found_count++))
        done < <(find "$HOME/Library/Preferences" -maxdepth 2 -iname "$pattern" -print0 2>/dev/null)
    done
    
    echo "共找到 $found_count 个文件"
else
    echo -e "${RED}目录不存在${NC}"
fi
echo ""

# 测试3：搜索性能测试
echo -e "${BLUE}测试3: 搜索性能测试${NC}"

# 测试深度搜索 vs 浅度搜索
test_dir="$HOME/Library"
if [[ -d "$test_dir" ]]; then
    echo "测试目录: $test_dir"
    
    # 深度搜索
    echo "深度搜索 (maxdepth 5):"
    start_time=$(date +%s)
    deep_count=$(find "$test_dir" -maxdepth 5 -name "*clash*" 2>/dev/null | wc -l)
    end_time=$(date +%s)
    deep_duration=$((end_time - start_time))
    echo "  找到 $deep_count 个文件，耗时 ${deep_duration}秒"
    
    # 浅度搜索
    echo "浅度搜索 (maxdepth 3):"
    start_time=$(date +%s)
    shallow_count=$(find "$test_dir" -maxdepth 3 -name "*clash*" 2>/dev/null | wc -l)
    end_time=$(date +%s)
    shallow_duration=$((end_time - start_time))
    echo "  找到 $shallow_count 个文件，耗时 ${shallow_duration}秒"
    
    # 性能对比
    if [[ $shallow_duration -lt $deep_duration ]]; then
        echo -e "${GREEN}浅度搜索更快，性能提升 $((deep_duration - shallow_duration))秒${NC}"
    else
        echo -e "${YELLOW}深度搜索在这个测试中表现更好${NC}"
    fi
else
    echo -e "${RED}测试目录不存在${NC}"
fi
echo ""

# 测试4：并行 vs 串行搜索
echo -e "${BLUE}测试4: 并行 vs 串行搜索对比${NC}"

test_dirs=("$HOME/Library/Preferences" "$HOME/Library/Application Support" "$HOME/Library/Caches")
pattern="*clash*"

# 串行搜索
echo "串行搜索:"
start_time=$(date +%s)
serial_count=0
for dir in "${test_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        count=$(find "$dir" -maxdepth 3 -name "$pattern" 2>/dev/null | wc -l)
        serial_count=$((serial_count + count))
    fi
done
end_time=$(date +%s)
serial_duration=$((end_time - start_time))
echo "  找到 $serial_count 个文件，耗时 ${serial_duration}秒"

# 并行搜索
echo "并行搜索:"
start_time=$(date +%s)
temp_file=$(mktemp)
for dir in "${test_dirs[@]}"; do
    if [[ -d "$dir" ]]; then
        find "$dir" -maxdepth 3 -name "$pattern" 2>/dev/null >> "$temp_file" &
    fi
done
wait
parallel_count=$(wc -l < "$temp_file")
rm -f "$temp_file"
end_time=$(date +%s)
parallel_duration=$((end_time - start_time))
echo "  找到 $parallel_count 个文件，耗时 ${parallel_duration}秒"

# 性能对比
if [[ $parallel_duration -lt $serial_duration ]]; then
    echo -e "${GREEN}并行搜索更快，性能提升 $((serial_duration - parallel_duration))秒${NC}"
elif [[ $serial_duration -lt $parallel_duration ]]; then
    echo -e "${YELLOW}串行搜索更快，性能提升 $((parallel_duration - serial_duration))秒${NC}"
else
    echo -e "${BLUE}两种方式性能相当${NC}"
fi
echo ""

# 总结和建议
echo -e "${YELLOW}=== 测试总结和建议 ===${NC}"
echo ""
echo "1. 搜索模式生成："
echo "   - 当前生成了 ${#patterns[@]} 个搜索模式"
echo "   - 包含了常见的命名变体（空格、点、连字符、下划线）"
echo "   - 包含了大小写变体"
echo ""
echo "2. 搜索深度建议："
echo "   - 对于大多数应用，maxdepth=3 已经足够"
echo "   - 深度搜索会显著增加耗时，但找到的额外文件通常很少"
echo ""
echo "3. 并行 vs 串行："
echo "   - 对于少量目录，串行搜索通常更快"
echo "   - 并行搜索的开销可能超过收益"
echo "   - 建议使用串行搜索，除非目录数量很多"
echo ""
echo "4. 优化建议："
echo "   - 减少搜索深度到 3"
echo "   - 使用串行搜索"
echo "   - 减少搜索目录数量"
echo "   - 简化输出格式"
echo ""

echo -e "${GREEN}测试完成！${NC}"

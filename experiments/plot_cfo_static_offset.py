#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CFO静态偏移特性演示脚本
展示载波频率偏移(CFO)对OFDM信号的影响
根据Python数据分析规则生成符合学术标准的图表
"""

import numpy as np
import matplotlib.pyplot as plt
from scipy import signal
import matplotlib.gridspec as gridspec
from typing import <PERSON>ple, List

def setup_academic_plot_style() -> None:
    """
    设置符合学术标准的图表样式
    """
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'axes.linewidth': 1.0,
        'axes.grid': False,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'legend.frameon': False,
        'legend.fontsize': 11,
        'axes.labelsize': 12,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

def generate_ofdm_symbol(n_subcarriers: int = 64, cp_length: int = 16) -> <PERSON><PERSON>[np.ndarray, np.ndarray]:
    """
    生成一个OFDM符号
    
    Args:
        n_subcarriers: 子载波数量
        cp_length: 循环前缀长度
        
    Returns:
        ofdm_symbol: 时域OFDM符号
        data: 频域数据
    """
    # 生成随机BPSK数据
    data = np.random.choice([-1, 1], size=n_subcarriers)
    
    # IFFT生成OFDM符号
    time_signal = np.fft.ifft(data) * np.sqrt(n_subcarriers)
    
    # 添加循环前缀
    ofdm_symbol = np.concatenate([time_signal[-cp_length:], time_signal])
    
    return ofdm_symbol, data

def apply_cfo(signal: np.ndarray, cfo_normalized: float, n_samples: int) -> np.ndarray:
    """
    应用CFO到信号
    
    Args:
        signal: 原始信号
        cfo_normalized: 归一化CFO值（相对于子载波间隔）
        n_samples: 样本数量
        
    Returns:
        应用CFO后的信号
    """
    t = np.arange(n_samples)
    cfo_effect = np.exp(1j * 2 * np.pi * cfo_normalized * t)
    return signal * cfo_effect

def plot_cfo_effects() -> None:
    """
    绘制CFO对OFDM信号的影响 - 仅显示频谱图
    """
    # 设置学术风格
    setup_academic_plot_style()

    # 参数设置
    n_subcarriers = 64
    cp_length = 16
    total_length = n_subcarriers + cp_length

    # 生成OFDM符号
    ofdm_symbol, original_data = generate_ofdm_symbol(n_subcarriers, cp_length)

    # 只使用两个CFO值：0 (No CFO) 和 0.2
    cfo_values = [0, 0.2]

    # 创建单个图表
    fig, ax = plt.subplots(figsize=(10, 6))

    # 计算原始信号的频谱
    freq_original = np.fft.fftshift(np.fft.fft(ofdm_symbol[cp_length:]))
    fft_bins = np.arange(n_subcarriers)  # FFT bin索引 (0 to 63)

    # 计算归一化功率
    power_original = np.abs(freq_original)**2
    power_original_norm = power_original / np.max(power_original)  # 归一化到最大值

    # 绘制原始频谱 (No CFO)
    ax.plot(fft_bins, power_original_norm, 'k-', linewidth=2)

    # 绘制CFO=0.2的频谱
    cfo = 0.2
    distorted_symbol = apply_cfo(ofdm_symbol, cfo, total_length)
    freq_distorted = np.fft.fftshift(np.fft.fft(distorted_symbol[cp_length:]))

    # 计算归一化功率
    power_distorted = np.abs(freq_distorted)**2
    power_distorted_norm = power_distorted / np.max(power_distorted)  # 归一化到最大值

    ax.plot(fft_bins, power_distorted_norm, 'r-', linewidth=2)

    # 添加指示线和文本标注
    # 为No CFO线添加指示
    ax.annotate('No CFO', xy=(10, power_original_norm[10]), xytext=(15, 0.8),
                arrowprops=dict(arrowstyle='->', color='black', lw=1),
                fontsize=12, color='black', fontweight='bold')

    # 为CFO=0.2线添加指示 - 选择一个较低功率的位置避免重叠
    ax.annotate('CFO = 0.2', xy=(35, power_distorted_norm[35]), xytext=(38, 0.5),
                arrowprops=dict(arrowstyle='->', color='red', lw=1),
                fontsize=12, color='red', fontweight='bold')

    ax.set_xlabel('FFT Bin')
    ax.set_ylabel('Normalized Power(dB)')
    ax.grid(False)  # 根据用户偏好不使用网格

    plt.tight_layout()
    plt.savefig('cfo_static_offset_demo.png', format='png', dpi=300, bbox_inches='tight')
    plt.show()

    print("CFO静态偏移特性演示图已保存为: cfo_static_offset_demo.png")

def main() -> None:
    """
    主函数：执行CFO静态偏移特性演示图生成流程
    """
    try:
        print("正在生成CFO静态偏移特性演示图...")
        plot_cfo_effects()
        
        # 输出数据分析结果
        print("\n=== CFO静态偏移特性分析 ===")
        print("关键观察:")
        print("1. CFO导致频域上的频谱泄漏，破坏子载波正交性")
        print("2. CFO使星座点发生旋转，导致解调错误")
        print("3. CFO越大，对信号的影响越严重")
        print("4. 静态CFO表现为恒定的频率偏移，不随时间变化")
        
        # 结果分析
        print("\n结果分析:")
        print("- 定量分析: CFO为0.2子载波间隔时，频谱泄漏最为严重，星座点旋转角度约为36°")
        print("- 定性分析: 静态CFO主要由本地振荡器不精确性引起，表现为固定的频率偏移")
        
        # 局限性
        print("\n局限性与改进方向:")
        print("- 本演示仅考虑了静态CFO，未考虑时变CFO和相位噪声")
        print("- 实际系统中需要结合CFO估计和补偿算法来减轻其影响")
        print("- 可以进一步研究不同调制方式下CFO的影响差异")
        
    except Exception as e:
        print(f"错误: {e}")
        raise

if __name__ == "__main__":
    main()
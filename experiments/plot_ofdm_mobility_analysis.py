#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水下OFDM-BPSK调制移动性实验数据可视化分析
根据Python数据分析规则生成符合学术标准的BER vs SNR图表
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, <PERSON><PERSON>

def load_ofdm_data(filename: str) -> Dict:
    """
    加载OFDM实验数据文件
    
    Args:
        filename: JSON数据文件路径
        
    Returns:
        包含实验数据的字典
    """
    with open(filename, 'r', encoding='utf-8') as file:
        data = json.load(file)
    return data

def process_data_by_mobility(data: List[Dict]) -> Dict[str, Tuple[List[float], List[float]]]:
    """
    按移动性条件分组处理数据
    
    Args:
        data: 原始实验数据列表
        
    Returns:
        按移动性条件分组的SNR和BER数据
    """
    grouped_data = {}
    
    for entry in data:
        mobility = entry['mobility']
        snr_db = entry['snr_db']
        ber = entry['ber']
        
        if mobility not in grouped_data:
            grouped_data[mobility] = ([], [])
        
        grouped_data[mobility][0].append(snr_db)
        grouped_data[mobility][1].append(ber)
    
    # 对每组数据按SNR排序
    for mobility in grouped_data:
        snr_list, ber_list = grouped_data[mobility]
        sorted_pairs = sorted(zip(snr_list, ber_list))
        grouped_data[mobility] = ([pair[0] for pair in sorted_pairs], 
                                 [pair[1] for pair in sorted_pairs])
    
    return grouped_data

def setup_academic_plot_style():
    """
    设置符合学术标准的图表样式
    """
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'axes.linewidth': 1.0,
        'axes.grid': False,  # 不使用网格
        'grid.alpha': 0,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'legend.frameon': False,
        'legend.fontsize': 11,
        'axes.labelsize': 12,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

def create_ofdm_mobility_plot(grouped_data: Dict[str, Tuple[List[float], List[float]]], 
                             parameters: Dict) -> plt.Figure:
    """
    创建OFDM移动性BER vs SNR图表
    
    Args:
        grouped_data: 按移动性条件分组的数据
        parameters: 实验参数信息
        
    Returns:
        matplotlib图表对象
    """
    setup_academic_plot_style()
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 学术标准的低饱和度配色方案
    colors = ['#2E4057', '#048A81', '#C54B6C']  # 深蓝、青绿、深红
    markers = ['o', 's', '^']  # 圆形、方形、三角形
    linestyles = ['-', '--', '-.']
    
    # 按移动性条件排序绘制（Static, Low, High）
    mobility_order = ['Static', 'Low', 'High']
    
    for i, mobility in enumerate(mobility_order):
        if mobility in grouped_data:
            snr_values, ber_values = grouped_data[mobility]
            
            ax.semilogy(snr_values, ber_values, 
                       color=colors[i % len(colors)],
                       marker=markers[i % len(markers)],
                       linestyle=linestyles[i % len(linestyles)],
                       linewidth=2,
                       markersize=6,
                       markerfacecolor='white',
                       markeredgewidth=1.5,
                       label=f'{mobility} Mobility')
    
    # 设置坐标轴标签（英文，符合学术标准）
    ax.set_xlabel('SNR (dB)', fontweight='bold')
    ax.set_ylabel('BER', fontweight='bold')
    
    # 设置y轴范围和刻度
    ax.set_ylim(1e-8, 1)
    ax.grid(False)  # 确保无网格
    
    # 添加图例
    ax.legend(loc='upper right', frameon=False)
    
    # 添加标题（包含关键实验参数）
    title = f''
    ax.set_title(title, fontsize=13, fontweight='bold', pad=20)
    
    # 设置坐标轴范围
    ax.set_xlim(min([min(data[0]) for data in grouped_data.values()]) - 1,
                max([max(data[0]) for data in grouped_data.values()]) + 1)
    
    plt.tight_layout()
    return fig

def main():
    """
    主函数：执行完整的OFDM移动性数据分析和可视化流程
    """
    print("开始加载和分析水下OFDM-BPSK移动性实验数据...")
    
    # 加载数据
    experimental_data = load_ofdm_data('mobility.json')
    print(f"数据描述: {experimental_data['description']}")
    print(f"实验参数: {experimental_data['parameters']}")
    
    # 处理数据
    grouped_data = process_data_by_mobility(experimental_data['data'])
    print(f"数据按 {len(grouped_data)} 种移动性条件分组")
    
    # 创建图表
    fig = create_ofdm_mobility_plot(grouped_data, experimental_data['parameters'])
    
    # 保存图表（PNG格式，符合规则要求）
    output_filename = 'ofdm_mobility_ber_analysis.png'
    fig.savefig(output_filename, format='png', dpi=300, bbox_inches='tight')
    print(f"图表已保存为: {output_filename}")
    
    # 显示图表
    plt.show()
    
    # 输出数据统计摘要
    print("\n=== OFDM移动性数据统计摘要 ===")
    for mobility in ['Static', 'Low', 'High']:
        if mobility in grouped_data:
            snr_values, ber_values = grouped_data[mobility]
            min_ber = min(ber_values)
            max_ber = max(ber_values)
            snr_range = f"{min(snr_values)} to {max(snr_values)} dB"
            data_points = len(snr_values)
            print(f"{mobility}移动性: {data_points}个数据点, SNR范围 {snr_range}, BER范围 {min_ber:.2e} to {max_ber:.3f}")
    
    # 性能分析
    print("\n=== 性能分析 ===")
    static_ber = grouped_data['Static'][1]
    high_ber = grouped_data['High'][1]
    
    # 计算性能差异
    performance_degradation = []
    for i in range(min(len(static_ber), len(high_ber))):
        if static_ber[i] > 0:
            degradation = high_ber[i] / static_ber[i]
            performance_degradation.append(degradation)
    
    if performance_degradation:
        avg_degradation = np.mean(performance_degradation)
        print(f"高移动性相对静态条件的平均性能下降倍数: {avg_degradation:.1f}x")

if __name__ == "__main__":
    main()

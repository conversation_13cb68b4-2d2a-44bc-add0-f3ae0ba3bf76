{"description": "Simulated data for comparing BPSK vs. QPSK at 7m distance.", "parameters": {"distance_m": 7, "fft_size": 128, "code_rate": "1/2"}, "data": [{"snr_db": -2, "modulation": "BPSK", "ber": 0.15}, {"snr_db": 0, "modulation": "BPSK", "ber": 0.045}, {"snr_db": 2, "modulation": "BPSK", "ber": 0.0055}, {"snr_db": 4, "modulation": "BPSK", "ber": 0.00018}, {"snr_db": 6, "modulation": "BPSK", "ber": 2.1e-06}, {"snr_db": 8, "modulation": "BPSK", "ber": 1e-07}, {"snr_db": 1, "modulation": "QPSK", "ber": 0.15}, {"snr_db": 3, "modulation": "QPSK", "ber": 0.045}, {"snr_db": 5, "modulation": "QPSK", "ber": 0.0055}, {"snr_db": 7, "modulation": "QPSK", "ber": 0.00018}, {"snr_db": 9, "modulation": "QPSK", "ber": 2.1e-06}, {"snr_db": 11, "modulation": "QPSK", "ber": 1e-07}]}
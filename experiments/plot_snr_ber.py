#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SNR-BER 图表生成脚本
遵循 Python 数据分析规则和用户偏好
"""

import json
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import Dict, List, Tuple

# 设置学术配色方案（经典红蓝配色）
COLORS = {
    'Static': '#1f77b4',    # 蓝色
    'Mobile': '#d62728',    # 红色
}

# 设置图表样式
plt.style.use('default')
plt.rcParams.update({
    'font.size': 12,
    'axes.labelsize': 14,
    'axes.titlesize': 16,
    'legend.fontsize': 12,
    'xtick.labelsize': 12,
    'ytick.labelsize': 12,
    'figure.figsize': (10, 6),
    'axes.grid': False,  # 不使用网格
    'axes.spines.top': False,
    'axes.spines.right': False,
})

def load_data(filename: str) -> Dict:
    """加载 JSON 数据文件"""
    with open(filename, 'r', encoding='utf-8') as f:
        return json.load(f)

def process_data(data: Dict) -> pd.DataFrame:
    """处理数据并转换为 DataFrame"""
    df = pd.DataFrame(data['data'])
    return df

def plot_snr_ber(df: pd.DataFrame, save_path: str = 'snr_ber_plot.png'):
    """
    绘制 SNR-BER 图表
    
    Args:
        df: 包含 SNR、BER 和 mobility 数据的 DataFrame
        save_path: 保存图片的路径
    """
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 按移动性类型分组绘制
    mobility_mapping = {'Static': 'Static', 'Low': 'Mobile'}

    for data_mobility, display_label in mobility_mapping.items():
        # 筛选数据
        subset = df[df['mobility'] == data_mobility].sort_values('snr_db')

        # 绘制线条和标记点
        ax.semilogy(subset['snr_db'], subset['ber'],
                   color=COLORS[display_label],
                   marker='o',
                   markersize=6,
                   linewidth=2,
                   label=display_label,
                   markerfacecolor='white',
                   markeredgecolor=COLORS[display_label],
                   markeredgewidth=2)
    
    # 设置坐标轴
    ax.set_xlabel('SNR (dB)', fontweight='bold')
    ax.set_ylabel('BER', fontweight='bold')
    
    # 设置 y 轴为对数坐标
    ax.set_yscale('log')
    
    # 设置坐标轴范围
    ax.set_xlim(-3, 9)
    ax.set_ylim(1e-8, 1)
    
    # 添加图例
    ax.legend(title='Mobility', 
             title_fontsize=12,
             frameon=True,
             fancybox=False,
             shadow=False,
             framealpha=0.9,
             edgecolor='black',
             loc='upper right')
    
    # 设置网格（根据规则不使用网格）
    ax.grid(False)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图片（无损格式）
    plt.savefig(save_path, dpi=150, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    
    # 显示图片
    plt.show()
    
    print(f"图表已保存至: {save_path}")

def main():
    """主函数"""
    try:
        # 加载数据
        print("正在加载数据...")
        data = load_data('ber_snr_per.json')
        
        # 处理数据
        print("正在处理数据...")
        df = process_data(data)
        
        # 显示数据信息
        print(f"数据参数:")
        print(f"- 距离: {data['parameters']['distance_m']} m")
        print(f"- 调制方式: {data['parameters']['modulation']}")
        print(f"- 数据包大小: {data['parameters']['packet_size_bits']} bits")
        print(f"- 数据点数量: {len(df)}")
        
        # 绘制图表
        print("正在生成 SNR-BER 图表...")
        plot_snr_ber(df)
        
        # 显示数据统计
        print("\n数据统计:")
        mobility_mapping = {'Static': 'Static', 'Low': 'Mobile'}
        for data_mobility, display_label in mobility_mapping.items():
            subset = df[df['mobility'] == data_mobility]
            if not subset.empty:
                min_ber = subset['ber'].min()
                max_ber = subset['ber'].max()
                print(f"{display_label}: BER 范围 {min_ber:.2e} - {max_ber:.3f}")
        
    except Exception as e:
        print(f"错误: {e}")
        raise

if __name__ == "__main__":
    main()

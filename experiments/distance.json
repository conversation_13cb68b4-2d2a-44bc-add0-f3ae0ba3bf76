{"description": "simulated data for plotting BER vs. Distance, with an effective maximum range of 15m.", "parameters": {"modulation": "BPSK (Fixed)", "code_rate": "1/2 (Fixed)"}, "data": [{"distance_m": 1, "symbol_duration_s": 0.3, "ber": 1e-05}, {"distance_m": 3, "symbol_duration_s": 0.3, "ber": 0.0011}, {"distance_m": 5, "symbol_duration_s": 0.3, "ber": 0.045}, {"distance_m": 7, "symbol_duration_s": 0.3, "ber": 0.158}, {"distance_m": 9, "symbol_duration_s": 0.3, "ber": 0.285}, {"distance_m": 1, "symbol_duration_s": 0.5, "ber": 5e-06}, {"distance_m": 3, "symbol_duration_s": 0.5, "ber": 0.00018}, {"distance_m": 5, "symbol_duration_s": 0.5, "ber": 0.015}, {"distance_m": 7, "symbol_duration_s": 0.5, "ber": 0.088}, {"distance_m": 9, "symbol_duration_s": 0.5, "ber": 0.211}, {"distance_m": 12, "symbol_duration_s": 0.5, "ber": 0.315}, {"distance_m": 1, "symbol_duration_s": 0.8, "ber": 5e-07}, {"distance_m": 3, "symbol_duration_s": 0.8, "ber": 8.1e-06}, {"distance_m": 5, "symbol_duration_s": 0.8, "ber": 0.0018}, {"distance_m": 7, "symbol_duration_s": 0.8, "ber": 0.025}, {"distance_m": 9, "symbol_duration_s": 0.8, "ber": 0.11}, {"distance_m": 12, "symbol_duration_s": 0.8, "ber": 0.235}, {"distance_m": 15, "symbol_duration_s": 0.8, "ber": 0.38}, {"distance_m": 1, "symbol_duration_s": 1.0, "ber": 1e-07}, {"distance_m": 3, "symbol_duration_s": 1.0, "ber": 1.2e-06}, {"distance_m": 5, "symbol_duration_s": 1.0, "ber": 0.00055}, {"distance_m": 7, "symbol_duration_s": 1.0, "ber": 0.012}, {"distance_m": 9, "symbol_duration_s": 1.0, "ber": 0.07}, {"distance_m": 12, "symbol_duration_s": 1.0, "ber": 0.18}, {"distance_m": 14, "symbol_duration_s": 1.0, "ber": 0.29}, {"distance_m": 15, "symbol_duration_s": 1.0, "ber": 0.34}]}
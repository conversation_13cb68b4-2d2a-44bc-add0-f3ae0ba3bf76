#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BPSK与QPSK调制方式性能对比分析
根据Python数据分析规则生成符合学术标准的BER vs SNR对比图表
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

def load_modulation_data(filename: str) -> Dict:
    """
    加载调制方式对比数据文件
    
    Args:
        filename: JSON数据文件路径
        
    Returns:
        包含实验数据的字典
    """
    with open(filename, 'r', encoding='utf-8') as file:
        data = json.load(file)
    return data

def process_data_by_modulation(data: List[Dict]) -> Dict[str, Tuple[List[float], List[float]]]:
    """
    按调制方式分组处理数据
    
    Args:
        data: 原始实验数据列表
        
    Returns:
        按调制方式分组的SNR和BER数据
    """
    grouped_data = {}
    
    for entry in data:
        modulation = entry['modulation']
        snr_db = entry['snr_db']
        ber = entry['ber']
        
        if modulation not in grouped_data:
            grouped_data[modulation] = ([], [])
        
        grouped_data[modulation][0].append(snr_db)
        grouped_data[modulation][1].append(ber)
    
    # 对每组数据按SNR排序
    for modulation in grouped_data:
        snr_list, ber_list = grouped_data[modulation]
        sorted_pairs = sorted(zip(snr_list, ber_list))
        grouped_data[modulation] = ([pair[0] for pair in sorted_pairs], 
                                   [pair[1] for pair in sorted_pairs])
    
    return grouped_data

def setup_academic_plot_style():
    """
    设置符合学术标准的图表样式
    """
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'axes.linewidth': 1.0,
        'axes.grid': False,  # 不使用网格
        'grid.alpha': 0,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'legend.frameon': False,
        'legend.fontsize': 11,
        'axes.labelsize': 12,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

def create_modulation_comparison_plot(grouped_data: Dict[str, Tuple[List[float], List[float]]], 
                                     parameters: Dict) -> plt.Figure:
    """
    创建BPSK与QPSK调制方式对比图表
    
    Args:
        grouped_data: 按调制方式分组的数据
        parameters: 实验参数信息
        
    Returns:
        matplotlib图表对象
    """
    setup_academic_plot_style()
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 鲜明饱满有区分度的配色方案
    colors = {'BPSK': '#1E90FF', 'QPSK': '#FF4500'}  # 天蓝色、橙红色
    markers = {'BPSK': 'o', 'QPSK': 's'}  # 圆形、方形
    linestyles = {'BPSK': '-', 'QPSK': '--'}  # 实线、虚线
    
    # 按调制方式绘制
    for modulation in ['BPSK', 'QPSK']:
        if modulation in grouped_data:
            snr_values, ber_values = grouped_data[modulation]
            
            ax.semilogy(snr_values, ber_values, 
                       color=colors[modulation],
                       marker=markers[modulation],
                       linestyle=linestyles[modulation],
                       linewidth=3,
                       markersize=8,
                       markerfacecolor='white',
                       markeredgewidth=2,
                       label=modulation)
    
    # 设置坐标轴标签（英文，符合学术标准）
    ax.set_xlabel('SNR (dB)', fontweight='bold')
    ax.set_ylabel('BER', fontweight='bold')
    
    # 设置y轴范围和刻度
    ax.set_ylim(1e-8, 1)
    ax.grid(False)  # 确保无网格
    
    # 添加图例
    ax.legend(loc='upper right', frameon=False)
    
    # 添加标题
    title = f''
    ax.set_title(title, fontsize=13, fontweight='bold', pad=20)
    
    # 设置坐标轴范围
    ax.set_xlim(min([min(data[0]) for data in grouped_data.values()]) - 1,
                max([max(data[0]) for data in grouped_data.values()]) + 1)
    
    plt.tight_layout()
    return fig

def calculate_snr_penalty(grouped_data: Dict[str, Tuple[List[float], List[float]]]) -> float:
    """
    计算QPSK相对于BPSK的SNR损失
    
    Args:
        grouped_data: 分组数据
        
    Returns:
        平均SNR损失（dB）
    """
    if 'BPSK' not in grouped_data or 'QPSK' not in grouped_data:
        return 0.0
    
    bpsk_snr, bpsk_ber = grouped_data['BPSK']
    qpsk_snr, qpsk_ber = grouped_data['QPSK']
    
    # 找到相同BER水平下的SNR差异
    snr_penalties = []
    
    for i, ber_target in enumerate(bpsk_ber):
        # 在QPSK数据中找到最接近的BER值
        closest_idx = min(range(len(qpsk_ber)), 
                         key=lambda j: abs(qpsk_ber[j] - ber_target))
        
        if abs(qpsk_ber[closest_idx] - ber_target) < ber_target * 0.5:  # 50%容差
            penalty = qpsk_snr[closest_idx] - bpsk_snr[i]
            snr_penalties.append(penalty)
    
    return np.mean(snr_penalties) if snr_penalties else 0.0

def main():
    """
    主函数：执行完整的BPSK vs QPSK对比分析和可视化流程
    """
    print("开始加载和分析BPSK与QPSK调制方式对比数据...")
    
    # 加载数据
    experimental_data = load_modulation_data('bpsk_qpsk.json')
    print(f"数据描述: {experimental_data['description']}")
    print(f"实验参数: {experimental_data['parameters']}")
    
    # 处理数据
    grouped_data = process_data_by_modulation(experimental_data['data'])
    print(f"数据按 {len(grouped_data)} 种调制方式分组")
    
    # 创建图表
    fig = create_modulation_comparison_plot(grouped_data, experimental_data['parameters'])
    
    # 保存图表（PNG格式，符合规则要求）
    output_filename = 'bpsk_qpsk_comparison.png'
    fig.savefig(output_filename, format='png', dpi=300, bbox_inches='tight')
    print(f"图表已保存为: {output_filename}")
    
    # 显示图表
    plt.show()
    
    # 输出数据统计摘要
    print("\n=== BPSK vs QPSK对比数据统计摘要 ===")
    for modulation in ['BPSK', 'QPSK']:
        if modulation in grouped_data:
            snr_values, ber_values = grouped_data[modulation]
            min_ber = min(ber_values)
            max_ber = max(ber_values)
            snr_range = f"{min(snr_values)} to {max(snr_values)} dB"
            data_points = len(snr_values)
            print(f"{modulation}: {data_points}个数据点, SNR范围 {snr_range}, BER范围 {min_ber:.2e} to {max_ber:.3f}")
    
    # 性能分析
    print("\n=== 调制方式性能分析 ===")
    snr_penalty = calculate_snr_penalty(grouped_data)
    print(f"QPSK相对于BPSK的平均SNR损失: {snr_penalty:.1f} dB")
    
    print("\n关键观察:")
    print("- QPSK在相同BER下需要更高的SNR（理论值约3dB）")
    print("- BPSK具有更好的功率效率")
    print("- QPSK具有更高的频谱效率（2倍数据速率）")
    print("- 两种调制方式都适用于水下通信系统")
    
    # 实际应用建议
    print("\n=== 实际应用建议 ===")
    print("选择BPSK当:")
    print("- 功率受限的应用场景")
    print("- 信道条件较差")
    print("- 对可靠性要求极高")
    
    print("\n选择QPSK当:")
    print("- 需要更高的数据传输速率")
    print("- 频谱资源有限")
    print("- 信道条件相对较好")

if __name__ == "__main__":
    main()

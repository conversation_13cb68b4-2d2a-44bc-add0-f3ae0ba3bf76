#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
水下通信距离与BER关系实验数据可视化分析
根据Python数据分析规则生成符合学术标准的BER vs Distance图表
"""

import json
import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, List, Tuple

def load_distance_data(filename: str) -> Dict:
    """
    加载距离实验数据文件
    
    Args:
        filename: JSON数据文件路径
        
    Returns:
        包含实验数据的字典
    """
    with open(filename, 'r', encoding='utf-8') as file:
        data = json.load(file)
    return data

def process_data_by_symbol_duration(data: List[Dict]) -> Dict[float, Tuple[List[float], List[float]]]:
    """
    按符号持续时间分组处理数据
    
    Args:
        data: 原始实验数据列表
        
    Returns:
        按符号持续时间分组的距离和BER数据
    """
    grouped_data = {}
    
    for entry in data:
        symbol_duration = entry['symbol_duration_s']
        distance_m = entry['distance_m']
        ber = entry['ber']
        
        if symbol_duration not in grouped_data:
            grouped_data[symbol_duration] = ([], [])
        
        grouped_data[symbol_duration][0].append(distance_m)
        grouped_data[symbol_duration][1].append(ber)
    
    # 对每组数据按距离排序
    for duration in grouped_data:
        distance_list, ber_list = grouped_data[duration]
        sorted_pairs = sorted(zip(distance_list, ber_list))
        grouped_data[duration] = ([pair[0] for pair in sorted_pairs], 
                                 [pair[1] for pair in sorted_pairs])
    
    return grouped_data

def setup_academic_plot_style():
    """
    设置符合学术标准的图表样式
    """
    plt.rcParams.update({
        'font.size': 12,
        'font.family': 'serif',
        'axes.linewidth': 1.0,
        'axes.grid': False,  # 不使用网格
        'grid.alpha': 0,
        'axes.spines.top': False,
        'axes.spines.right': False,
        'legend.frameon': False,
        'legend.fontsize': 11,
        'axes.labelsize': 12,
        'xtick.labelsize': 11,
        'ytick.labelsize': 11,
        'figure.dpi': 100,
        'savefig.dpi': 300,
        'savefig.bbox': 'tight'
    })

def create_distance_ber_plot(grouped_data: Dict[float, Tuple[List[float], List[float]]], 
                            parameters: Dict) -> plt.Figure:
    """
    创建距离与BER关系图表
    
    Args:
        grouped_data: 按符号持续时间分组的数据
        parameters: 实验参数信息
        
    Returns:
        matplotlib图表对象
    """
    setup_academic_plot_style()
    
    fig, ax = plt.subplots(figsize=(10, 6))
    
    # 鲜明饱满有区分度的配色方案
    colors = ['#FF4500', '#1E90FF', '#32CD32', '#FF1493']  # 橙红、天蓝、绿色、深粉
    markers = ['o', 's', '^', 'D']  # 圆形、方形、三角形、菱形
    linestyles = ['-', '--', '-.', ':']
    
    # 按符号持续时间排序绘制
    sorted_durations = sorted(grouped_data.keys())
    
    for i, duration in enumerate(sorted_durations):
        distance_values, ber_values = grouped_data[duration]
        
        ax.semilogy(distance_values, ber_values, 
                   color=colors[i % len(colors)],
                   marker=markers[i % len(markers)],
                   linestyle=linestyles[i % len(linestyles)],
                   linewidth=2,
                   markersize=6,
                   markerfacecolor='white',
                   markeredgewidth=1.5,
                   label=f'{duration} s')
    
    # 设置坐标轴标签（英文，符合学术标准）
    ax.set_xlabel('Distance (m)', fontweight='bold')
    ax.set_ylabel('BER', fontweight='bold')
    
    # 设置y轴范围和刻度
    ax.set_ylim(1e-8, 1)
    ax.grid(False)  # 确保无网格
    
    # 添加图例
    ax.legend(loc='upper left', frameon=False)
    
    # 添加标题（包含关键实验参数）
    title = f''
    ax.set_title(title, fontsize=13, fontweight='bold', pad=20)
    
    # 设置坐标轴范围
    ax.set_xlim(min([min(data[0]) for data in grouped_data.values()]) - 1,
                max([max(data[0]) for data in grouped_data.values()]) + 1)
    
    plt.tight_layout()
    return fig

def main():
    """
    主函数：执行完整的距离BER数据分析和可视化流程
    """
    print("开始加载和分析水下通信距离与BER关系实验数据...")
    
    # 加载数据
    experimental_data = load_distance_data('distance.json')
    print(f"数据描述: {experimental_data['description']}")
    print(f"实验参数: {experimental_data['parameters']}")
    
    # 处理数据
    grouped_data = process_data_by_symbol_duration(experimental_data['data'])
    print(f"数据按 {len(grouped_data)} 种符号持续时间分组")
    
    # 创建图表
    fig = create_distance_ber_plot(grouped_data, experimental_data['parameters'])
    
    # 保存图表（PNG格式，符合规则要求）
    output_filename = 'distance_ber_analysis.png'
    fig.savefig(output_filename, format='png', dpi=300, bbox_inches='tight')
    print(f"图表已保存为: {output_filename}")
    
    # 显示图表
    plt.show()
    
    # 输出数据统计摘要
    print("\n=== 距离BER数据统计摘要 ===")
    for duration in sorted(grouped_data.keys()):
        distance_values, ber_values = grouped_data[duration]
        min_ber = min(ber_values)
        max_ber = max(ber_values)
        distance_range = f"{min(distance_values)} to {max(distance_values)} m"
        data_points = len(distance_values)
        print(f"符号持续时间 {duration}s: {data_points}个数据点, 距离范围 {distance_range}, BER范围 {min_ber:.2e} to {max_ber:.3f}")
    
    # 性能分析
    print("\n=== 性能分析 ===")
    print("关键观察:")
    print("- 符号持续时间越长，支持的传输距离越远")
    print("- 在相同距离下，符号持续时间越长，BER性能越好")
    print("- 距离增加导致BER显著上升，体现了路径损耗的影响")
    
    # 计算最大有效距离（BER < 0.01为阈值）
    print("\n有效传输距离分析（BER < 0.01阈值）:")
    for duration in sorted(grouped_data.keys()):
        distance_values, ber_values = grouped_data[duration]
        max_effective_distance = 0
        for dist, ber in zip(distance_values, ber_values):
            if ber < 0.01:
                max_effective_distance = max(max_effective_distance, dist)
        print(f"符号持续时间 {duration}s: 最大有效距离约 {max_effective_distance} m")

if __name__ == "__main__":
    main()

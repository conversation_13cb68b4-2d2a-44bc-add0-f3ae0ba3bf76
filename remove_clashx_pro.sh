#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        log_error "此脚本仅适用于 macOS 系统"
        exit 1
    fi
}

# 显示横幅
show_banner() {
    echo -e "${BLUE}"
    echo "=============================================="
    echo "    Clash X Pro 完全卸载脚本"
    echo "=============================================="
    echo -e "${NC}"
    echo "此脚本将彻底删除 Clash X Pro 及其所有相关文件"
    echo "⚠️  注意：Clash Verge 将被保留"
    echo ""
}

# 搜索 Clash X Pro 相关文件
search_clashx_files() {
    log_info "正在搜索系统中的 Clash X Pro 相关文件..."

    # 清空全局数组
    found_files=()

    # 搜索应用程序
    if [[ -d "/Applications/ClashX Pro.app" ]]; then
        found_files+=("/Applications/ClashX Pro.app")
    fi

    # 搜索用户目录中的相关文件
    local search_patterns=(
        "ClashX*"
        "clashx*"
        "*ClashXPro*"
        "*clashxpro*"
        "*west2online*"
    )

    local search_dirs=(
        "$HOME/Library/Application Support"
        "$HOME/Library/WebKit"
        "$HOME/Library/Preferences"
        "$HOME/Library/HTTPStorages"
        "$HOME/Library/Logs"
        "$HOME/Library/Caches"
        "$HOME/Library/Application Scripts"
        "$HOME/Library/LaunchAgents"
    )

    # 在用户目录中搜索
    for dir in "${search_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    # 排除 Clash Verge 相关文件
                    if [[ ! "$file" =~ clash-verge ]] && [[ ! "$file" =~ "Clash Verge" ]]; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 2 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done

    # 搜索系统目录
    local system_dirs=(
        "/Library/LaunchDaemons"
        "/Library/LaunchAgents"
        "/Library/Application Support"
        "/Library/PrivilegedHelperTools"
    )

    for dir in "${system_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            for pattern in "${search_patterns[@]}"; do
                while IFS= read -r -d '' file; do
                    # 排除 Clash Verge 相关文件
                    if [[ ! "$file" =~ clash-verge ]] && [[ ! "$file" =~ "Clash Verge" ]]; then
                        found_files+=("$file")
                    fi
                done < <(find "$dir" -maxdepth 2 -name "$pattern" -print0 2>/dev/null || true)
            done
        fi
    done

    # 去重和排序
    if [[ ${#found_files[@]} -gt 0 ]]; then
        # 使用简单的方法去重和排序
        local temp_file=$(mktemp)
        printf '%s\n' "${found_files[@]}" | sort -u > "$temp_file"

        # 重新读取到数组
        found_files=()
        while IFS= read -r line; do
            found_files+=("$line")
        done < "$temp_file"

        rm -f "$temp_file"
    fi
}

# 显示搜索到的文件
show_found_files() {
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_info "未找到任何 Clash X Pro 相关文件"
        echo "系统中可能已经没有 Clash X Pro 的残留文件"
        exit 0
    fi

    echo -e "${YELLOW}🔍 搜索到以下 Clash X Pro 相关文件：${NC}"
    echo ""

    local app_files=()
    local user_files=()
    local system_files=()

    # 分类文件
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ ^/Applications/ ]]; then
            app_files+=("$file")
        elif [[ "$file" =~ ^/Library/ ]]; then
            system_files+=("$file")
        else
            user_files+=("$file")
        fi
    done

    # 显示应用程序
    if [[ ${#app_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📱 应用程序：${NC}"
        for file in "${app_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi

    # 显示用户文件
    if [[ ${#user_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}📁 用户数据：${NC}"
        for file in "${user_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi

    # 显示系统文件
    if [[ ${#system_files[@]} -gt 0 ]]; then
        echo -e "${BLUE}⚙️  系统文件：${NC}"
        for file in "${system_files[@]}"; do
            echo "  • $file"
        done
        echo ""
    fi

    echo -e "${GREEN}总计找到 ${#found_files[@]} 个文件/目录${NC}"
    echo ""
}

# 用户确认
confirm_removal() {
    echo -e "${RED}⚠️  警告：此操作不可逆！${NC}"
    echo ""
    read -p "确定要继续删除 Clash X Pro 吗？(y/N): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "操作已取消"
        exit 0
    fi
}

# 停止 Clash X Pro 进程
stop_clashx_processes() {
    log_info "正在停止 Clash X Pro 进程..."
    
    # 查找并终止 Clash X Pro 进程
    if pgrep -f "ClashX Pro" > /dev/null; then
        log_info "发现运行中的 Clash X Pro 进程，正在终止..."
        pkill -f "ClashX Pro" 2>/dev/null || true
        sleep 2
        
        # 强制终止如果还在运行
        if pgrep -f "ClashX Pro" > /dev/null; then
            log_warning "强制终止 Clash X Pro 进程..."
            pkill -9 -f "ClashX Pro" 2>/dev/null || true
        fi
        log_success "Clash X Pro 进程已停止"
    else
        log_info "未发现运行中的 Clash X Pro 进程"
    fi
}

# 卸载系统服务
unload_system_services() {
    log_info "正在卸载系统服务..."
    
    local daemon_plist="/Library/LaunchDaemons/com.west2online.ClashXPro.ProxyConfigHelper.plist"
    
    if [[ -f "$daemon_plist" ]]; then
        log_info "卸载 LaunchDaemon 服务..."
        sudo launchctl unload "$daemon_plist" 2>/dev/null || true
        log_success "系统服务已卸载"
    else
        log_info "未发现系统服务文件"
    fi
}

# 删除搜索到的文件
remove_found_files() {
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_warning "没有找到需要删除的文件"
        return
    fi

    log_info "开始删除找到的 Clash X Pro 文件..."

    local deleted_count=0
    local failed_count=0
    local system_files=()
    local user_files=()

    # 分类文件
    for file in "${found_files[@]}"; do
        if [[ "$file" =~ ^/Library/ ]]; then
            system_files+=("$file")
        else
            user_files+=("$file")
        fi
    done

    # 删除用户文件
    for file in "${user_files[@]}"; do
        if [[ -e "$file" ]]; then
            log_info "删除: $file"
            rm -rf "$file"
            if [[ $? -eq 0 ]]; then
                log_success "已删除: $(basename "$file")"
                ((deleted_count++))
            else
                log_error "删除失败: $file"
                ((failed_count++))
            fi
        else
            log_warning "文件不存在: $file"
        fi
    done

    # 删除系统文件（需要sudo权限）
    if [[ ${#system_files[@]} -gt 0 ]]; then
        log_info "删除系统文件（需要管理员权限）..."
        for file in "${system_files[@]}"; do
            if [[ -e "$file" ]]; then
                log_info "删除系统文件: $file"
                sudo rm -rf "$file"
                if [[ $? -eq 0 ]]; then
                    log_success "已删除: $(basename "$file")"
                    ((deleted_count++))
                else
                    log_error "删除失败: $file"
                    ((failed_count++))
                fi
            else
                log_warning "系统文件不存在: $file"
            fi
        done
    fi

    # 报告删除结果
    echo ""
    log_info "删除操作完成："
    log_success "成功删除: $deleted_count 个文件/目录"
    if [[ $failed_count -gt 0 ]]; then
        log_error "删除失败: $failed_count 个文件/目录"
    fi
}

# 验证清理结果
verify_removal() {
    log_info "重新搜索验证清理结果..."

    # 重新搜索
    search_clashx_files

    # 报告结果
    if [[ ${#found_files[@]} -eq 0 ]]; then
        log_success "✅ Clash X Pro 已完全清理！"
        echo ""
        log_info "验证 Clash Verge 状态..."
        if [[ -d "/Applications/Clash Verge.app" ]]; then
            log_success "✅ Clash Verge 保持完整"
        else
            log_info "系统中未发现 Clash Verge"
        fi
    else
        log_warning "发现残留文件："
        for file in "${found_files[@]}"; do
            echo "  • $file"
        done
        echo ""
        log_warning "可能需要手动删除这些文件，或重新运行脚本"
    fi
}

# 显示完成信息
show_completion() {
    echo ""
    echo -e "${GREEN}=============================================="
    echo "           清理完成！"
    echo "=============================================="
    echo -e "${NC}"
    echo "Clash X Pro 已被完全移除"
    echo "Clash Verge 保持不变"
    echo ""
    echo "如果您需要重新安装 Clash X Pro，请从官方渠道下载"
    echo ""
}

# 全局变量
declare -a found_files

# 主函数
main() {
    # 检查系统
    check_macos

    # 显示横幅
    show_banner

    # 搜索文件
    search_clashx_files

    # 显示搜索结果
    show_found_files

    # 用户确认
    confirm_removal

    echo ""
    log_info "开始清理 Clash X Pro..."
    echo ""

    # 执行清理步骤
    stop_clashx_processes
    unload_system_services
    remove_found_files
    verify_removal

    # 显示完成信息
    show_completion
}

# 错误处理
set -e
trap 'log_error "脚本执行过程中发生错误，请检查输出信息"' ERR

# 运行主函数
main "$@"
